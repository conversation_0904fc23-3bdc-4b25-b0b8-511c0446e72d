.taskManagerContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.fixedHeader {
  flex-shrink: 0; // 头部不收缩
}

.scrollableContent {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 70px; // 为多选按钮留出空间
}

.taskManagerContent {
  padding: 0px 16px;
  height: 100%;
}

.titleContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  margin-bottom: 12px;

  .title {
    font-size: 24px;
    color: var(--text-color, #000000);
    flex: 1;
  }

  .selectAllContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
  }
}

.emptySearch {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 48px;

  img {
    width: 64px;
    height: 64px;
    margin-bottom: 16px;
  }

  span {
    color: #999;
  }
}

.taskTab {
  background: var(--tab-bg-color);
  margin: 12px;
  border-radius: 16px;

  :global(.adm-capsule-tabs-header) {
    border-bottom: none
  }

  :global {
    .adm-capsule-tabs-tab-active {
      background-color: var(--tab-ac-bg-color) !important;
      color: var(--title-color) !important;
    }

    .adm-capsule-tabs-tab {
      color: var(--subtitle-text-color);
      background-color: transparent;
    }
  }
}

// 多选模式下的删除按钮
.multiSelectActions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background-color: #F7F7F7;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .delName {
      font-size: 10px;

    }
  }
}