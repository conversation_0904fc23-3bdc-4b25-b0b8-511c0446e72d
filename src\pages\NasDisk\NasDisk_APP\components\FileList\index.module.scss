.ListContainer {
  //  height: calc(100% - 196px);
  // scrollbar-width: none;
  // overflow-y: auto;
  background-color: var(--background-color);
  :global {
    .adm-list {
      --border-inner: none;
      --border-bottom: none;
      --border-top: none;
    }
    .adm-list-body {
      margin: 0 12px;
    }
    a.adm-list-item:active:not(.adm-list-item-disabled) {
      background-color: var(--background-color);
    }
    .adm-list-item{
      background-color: var(--background-color);
    }
  }
  .headerBar {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 15px;
    height: 44px;

    .title {
      flex: 1;
      font-size: 16px;
      font-weight: 500;
    }

    .selectAllButton {
      margin-left: 10px;
      padding: 4px 10px;
      border-radius: 15px;
      cursor: pointer;

      .selectAllText {
        font-size: 14px;
        color: var(--subtitle-text-color);
      }
    }

    button {
      padding: 8px 16px;
      border: none;
      background-color: transparent;
      color: var(--list-value-text-color);
      border-radius: 4px;
      cursor: pointer;
    }
  }

  .backButton {
    padding: 10px;
    background-color: #f5f5f5;
    border-bottom: 1px solid var(--thinLine-background-color);
    cursor: pointer;
    color: var(--primary-color);
    font-weight: bold;
  }

  .fileItem {
    display: flex;
    align-items: center;

    .fileIcon {
      margin-right: 12px;

      img {
        width: 24px;
        height: 24px;
      }
    }

    .fileInfo {
      flex: 1;

      .fileName {
        font-family: MiSans;
        font-weight: 500;
        font-size: 17px;
        line-height: 100%;
        letter-spacing: 0px;
        color: var(--text-color);
        padding: 4px 0;
        word-break: break-all; /* 强制在任意字符处换行 */
        word-wrap: break-word; /* 兼容性属性 */
        overflow-wrap: break-word; /* 现代标准属性 */
      }

      .fileTime {
        font-family: MiSans;
        font-weight: 400;
        font-size: 12px;
        line-height: 100%;
        letter-spacing: 0px;
        color: var(--list-value-text-color);
        padding: 4px 0;
      }
    }
  }

  .footerButtons {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    padding: 16px;
    background-color: var(--background-color);
    .locationButton {
      border-radius: 30px;
      background-color: transparent;
      border-color: var(--thinLine-background-color);
      color: var(--primary-color);
      margin-right: 16px;
      font-size: 12px;
      height: 50px;
      padding: 5px 16px;
      text-align: left;
    }
    .downloadButton {
      background-color: var(--primary-color);
      border-radius: 30px;
      border: none;
      max-width: 168px;
    }
    :global {
      .adm-button:active::before {
        opacity: 0;
      }
      .adm-button-disabled {
        background-color: var(--primary-color);
      }
    }

    .vipTip {
      position: absolute;
      top: 0;
      left: 45%;
      transform: translateX(-50%);
      background-color: #402c00;
      color: #e2ae1e;
      padding: 2px 5px;
      border-radius: 5px;
      font-size: 12px;
      border: 0.1px solid #e2ae1e;
      z-index: 10;
    }
  }

  .addButtonContainer {
    position: fixed;
    bottom: 90px;
    right: 32px;
  }

  .addButton {
    min-width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background-color: #34bbbf;
    border: none;
    color: white;
    font-size: 40px;
  }
}
.locatext {
  display: -webkit-box;
  display: -moz-box; /* 部分旧版 Firefox 需要 */
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical; /* Firefox 兼容 */
  -webkit-line-clamp: 2; /* WebKit/Blink 核心 */
  line-clamp: 2; /* 标准属性 */
  box-orient: vertical; /* 备用标准属性 (部分场景) */
}

.loadingText {
  margin-top: 12px;
  color: var(--adm-color-primary);
  font-size: 14px;
}

.vipModalContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
}

.vipModalText {
  font-size: 16px;
  text-align: center;
  line-height: 1.5;
  margin: 0;
}
