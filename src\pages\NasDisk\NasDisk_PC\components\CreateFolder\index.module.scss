.createFolderModal {
  :global(.ant-modal-content) {
    border-radius: 20px;
    overflow: hidden;
    background-color: var(--file-selector-bg);
  }

  :global(.ant-modal-header) {
    margin-bottom: 20px;
  }

  :global(.ant-modal-title) {
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    background-color: var(--file-selector-bg);
    color: var(--text-color);
  }
}

.inputWrapper {
  margin-bottom: 24px;
  background-color: var(--input-bg-color);
  ::placeholder {
    color: var(--list-value-text-color);
  }

  :global {
    .ant-input {
      background-color: var(--input-bg-color);
      color: var(--text-color);
    }
    .ant-input-affix-wrapper {
      padding: 10px;
      background-color: var(--input-bg-color);
      color: var(--text-color);
    }
    .ant-input-affix-wrapper > input.ant-input {
      color: var(--subtitle-text-color);
    }
  }

  :global(.ant-input) {
    border-radius: 8px;
    font-size: 14px;
    padding: 10px;
  }
}

.clearIcon {
  color: rgba(0, 0, 0, 0.25);
  cursor: pointer;

  &:hover {
    color: rgba(0, 0, 0, 0.45);
  }
}

.buttonGroup {
  display: flex;
  justify-content: space-between;
  :global {
    .ant-btn-variant-outlined:not(:disabled):not(.ant-btn-disabled):hover,
    .ant-btn-variant-dashed:not(:disabled):not(.ant-btn-disabled):hover {
      background-color: rgba(243, 244, 246, 1);
      color: rgba(74, 85, 104, 1);
      border: none;
    }
    .ant-btn-variant-solid:disabled,
    .ant-btn-variant-solid.ant-btn-disabled {
      opacity: 0.3;
      background-color: var(--primary-color);
      border: none;
      color: rgba(255, 255, 255, 1);
    }
    .ant-btn-variant-outlined:not(:disabled):not(.ant-btn-disabled):hover {
      background-color: var(--input-bg-color);
      color: var(--subtitle-text-color);
    }
  }
}

.cancelButton,
.confirmButton {
  flex: 1;
  height: 40px;
  border-radius: 10px;
  font-size: 14px;
  border: none;
}

.cancelButton {
  margin-right: 12px;
  background-color: var(--input-bg-color);
  color: var(--subtitle-text-color);
}

.confirmButton {
  margin-left: 12px;
  background-color: var(--primary-color);
  color: rgba(255, 255, 255, 1);
}
