import { PreloadImage } from "@/components/Image";
import styles from "./index.module.scss";
import { useCallback, useMemo, useState } from "react";
import more from "@/Resources/modal/more.png";
import more_dark from "@/Resources/modal/more_dark.png";
import { Form, Switch } from "antd";
import PopoverSelector, { Option } from "@/components/PopoverSelector";
import { useTheme } from "@/utils/themeDetector";
import event_error_img from "@/Resources/camera_poster/camera_manager_eventLookback_poster.png";
import event_error_img_dark from "@/Resources/camera_poster/camera_manager_eventLookback_poster_dark.png";


interface IRightOPt {
  type: 'status' | 'button' | 'switch'
  title: string,
  callback?: (v: any) => void
}

export interface IEventListCard {
  icon: string
  poster?: string
  rightOpt?: IRightOPt
  title: string
  subtitle: string
  titleColor?: string
  subtitleColor?: string
  moreOpt?: {
    moreOptOptions: (Option & { callback: (v: EventListCardProps) => void })[]
    curValue: any
  }
  posterCallback?: (v: any) => void
  posterActive?: boolean
  id?: string
  name?: string
  urls?: string[]
  file?: string
}

export type EventListCardProps = IEventListCard & {
  callback?: (item: IEventListCard) => void;
  needHeader?: boolean
};

const EventListCard = (props: EventListCardProps) => {
  const { icon, poster, title, subtitle, callback, rightOpt, titleColor, subtitleColor, moreOpt, posterCallback, posterActive, needHeader } = props;
  const { isDarkMode } = useTheme();

  const [moreOptIsShow, setMoreOptIsShow] = useState<boolean>(false);
  const [curValue, setCurValue] = useState<any>(moreOpt?.curValue);

  const RightOptComponent = useMemo(() => {
    if (rightOpt) {
      switch (rightOpt.type) {
        case 'status': return <div className={styles.eventListCard_rightOpt_status} style={{ cursor: rightOpt.callback ? 'pointer' : '' }} onClick={() => rightOpt.callback && rightOpt.callback(props)}>{rightOpt.title}</div>;
        case "button": return <div className={styles.eventListCard_rightOpt_button} style={{ cursor: rightOpt.callback ? 'pointer' : '' }} onClick={() => rightOpt.callback && rightOpt.callback(props)}>{rightOpt.title}</div>
        case "switch": return <Switch onChange={(checked) => rightOpt.callback && rightOpt.callback(checked)} />
        default: return <div className={styles.eventListCard_rightOpt_status}>{rightOpt.title}</div>;
      }
    }
  }, [props, rightOpt])

  const moreOptCallback = useCallback((v) => {
    const item = moreOpt?.moreOptOptions.find((it) => it.value === v);
    if (item) {
      item.callback(props);
    }
    setCurValue(v);
  }, [moreOpt?.moreOptOptions, props])

  return <div className={styles.eventListCard_container}>
    <div className={styles.eventListCard_content_container} style={{ cursor: callback ? 'pointer' : '' }} onClick={() => callback && callback(props)}>
      <div className={styles.eventListCard_icon} style={{ background: icon ? '' : 'rgba(238, 238, 238, 1)' }}>
        {icon ? <PreloadImage src={icon} alt="icon" /> : <></>}
      </div>
      <div className={styles.eventListCard_content}>
        <span className={styles.eventListCard_title} style={{ color: titleColor ? titleColor : '' }}>{title}</span>
        <span className={styles.eventListCard_subtitle} style={{ color: subtitleColor ? subtitleColor : '' }}>{subtitle}</span>
      </div>
    </div>
    {
      poster ?
        <div className={`${styles.eventListCard_poster}`} style={{ cursor: posterCallback ? 'pointer' : '' }} onClick={() => posterCallback && posterCallback(props)}>
          {
            posterActive && (
              <div className={styles.activeText}>播放中</div>
            )
          }
          <PreloadImage style={{ zIndex: 1 }} src={poster} alt="poster" needHeader={needHeader} errorImage={isDarkMode ? event_error_img_dark : event_error_img} />
        </div>
        : <></>
    }
    {
      rightOpt ? <div className={styles.eventListCard_rightOpt}>
        <Form.Item name={rightOpt.title} noStyle valuePropName="checked">
          {RightOptComponent}
        </Form.Item>
      </div>
        : <></>
    }
    {
      moreOpt ? <div className={styles.eventListCard_moreOpt} onClick={() => setMoreOptIsShow(true)}>
        <PopoverSelector value={curValue} visible={moreOptIsShow} onlySelect={true} onVisibleChange={setMoreOptIsShow} onChange={moreOptCallback} options={moreOpt.moreOptOptions}>
          <div style={{ width: '100%' }}>
            <PreloadImage src={isDarkMode ? more_dark : more} alt="more" />
          </div>
        </PopoverSelector>
      </div> : <></>
    }
  </div>
}

export default EventListCard;