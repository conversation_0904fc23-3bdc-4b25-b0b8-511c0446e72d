import { sendToEnvironment, sendDataToApp, sendStringToEnvironment } from "@/utils/microAppUtils";

const _PLAY_VIDEO = 'videoWall_playVideo';
const _DOWNLOAD_FILES = 'normal_download';
const _GET_USER_LIST = 'videoWall_getUserList';
const _Screen_Click = 'videoWall_clickScreen'; // 投屏
const _Screen_Get = 'videoWall_getScreenType'; // 获取投屏信息

interface IRes {
  code: number,
  msg: string,
  data?: any
}

interface IFileInfo {
  name: string;
  path: string;
  mtime?: string;
  size?: number;
}

// 新增视频项接口定义
export interface VideoItem {
  filter?: any;
  path: string;
  media_id: string;
  file_id: string;
  duration?: number; //视频时长
  position?: number;  //断点信息
  isCompelete?: number;   //是否完整播放
  audioIndex?: number;    //音轨信息
  subtitlePath?: string; //字幕路径
  total_time?: number; //视频总时长（秒）·
  subtitleType?: number;  //字幕类型，0表示内嵌字幕，
  subtitleIndex?: number;
}

// 用户信息接口定义
export interface UserInfo {
  headImg: string;
  uid: string;
  permission: 'admin' | 'user';
  nickname: string;
  group: string[];
  status: 'active' | 'inactive';
  create_time: string;
  delete_time: string;
  total_size: number;
  used_size: number;
  source: string;
}

// 用户列表响应接口
export interface UserListResponse {
  code: number;
  msg: string;
  data: {
    list: UserInfo[];
  };
}

/**
 * 调用播放视频
 *
 * @param videoList 视频文件信息数组，包含path、media_id、file_id、total_time
 * @param index 要播放的视频索引，默认为0
 * @param callback 回调函数，用于处理播放结果
 * @returns Promise对象，用于处理异步操作
 */
export const playVideo = async (videoList: VideoItem[], index: number = 0, callback: (res: IRes) => void): Promise<void> => {
  if (videoList.length < 1) return Promise.reject();
  return new Promise((rs, rj) => {
    sendToEnvironment(
      { methodName: _PLAY_VIDEO, params: { videoList, index } },
      {
        params: {
          cmd: "openvideo",
          index,
          list: videoList.map((video, idx) => ({
            name: video.path.split('/').pop() || `video_${idx}.mp4`, // 从路径中提取文件名
            path: video.path,
            total_time: video.total_time || 0, // 添加total_time参数
            file_id: video.file_id || 0,
            media_id: video.media_id || 0,
            filter: video.filter
          }))
        }
      },
      (response: IRes) => {
        if (response.code === 0) {
          callback(response)
          rs();
        } else {
          rj(new Error(`播放失败:${response.msg}`))
        }
      }
    )
  })
}

/**
 * TV端专用播放视频函数 - 传递字符串格式给后端
 *
 * @param videoList 视频文件信息数组，包含path、media_id、file_id、total_time
 * @param index 要播放的视频索引，默认为0
 * @param callback 回调函数，用于处理播放结果
 * @returns Promise对象，用于处理异步操作
 */
export const playVideoTV = async (videoList: VideoItem[], index: number = 0, callback: (res: IRes) => void): Promise<void> => {
  if (videoList.length < 1) return Promise.reject();
  return new Promise((rs, rj) => {
    // TV端：将整个参数对象都转换为JSON字符串
    const appParams = JSON.stringify({ videoList, index });

    const pcParams = JSON.stringify({
      cmd: "openvideo",
      index,
      list: videoList.map((video, idx) => ({
        name: video.path.split('/').pop() || `video_${idx}.mp4`, // 从路径中提取文件名
        path: video.path,
        total_time: video.total_time || 0, // 添加total_time参数
        file_id: video.file_id || 0,
        media_id: video.media_id || 0,
        filter: video.filter
      }))
    });

    sendStringToEnvironment(
      { methodName: _PLAY_VIDEO, paramsString: appParams },
      pcParams,
      (response: IRes) => {
        if (response.code === 0) {
          callback(response)
          rs();
        } else {
          rj(new Error(`播放失败:${response.msg}`))
        }
      }
    )
  })
}

/**
 * 获取用户列表
 *
 * @param callback 回调函数，用于处理获取用户列表的结果
 * @returns Promise对象，用于处理异步操作
 */
export const getUserList = async (callback: (res: UserListResponse) => void): Promise<void> => {
  return new Promise((rs, rj) => {
    sendToEnvironment(
      { methodName: _GET_USER_LIST, params: {} },
      {
        params: {
          cmd: "getUserList"
        }
      },
      (response: UserListResponse) => {
        if (response.code === 0) {
          callback(response);
          rs();
        } else {
          rj(new Error(`获取用户列表失败:${response.msg}`));
        }
      }
    )
  })
}

/**
 * 调用下载文件
 *
 * @param fileList 文件信息数组，包含路径、名称、修改时间、大小等信息
 * @param callback 回调函数，用于处理下载结果
 * @returns Promise对象，用于处理异步操作
 */
export const downloadFiles = async (fileList: IFileInfo[], callback: (res: IRes) => void): Promise<void> => {
  if (fileList.length < 1) return Promise.reject(new Error('文件列表不能为空'));
  return new Promise((rs, rj) => {
    sendToEnvironment(
      { methodName: _DOWNLOAD_FILES, params: { pathList: fileList.map(file => file.path) } },
      {
        params: {
          cmd: "download",
          spath: fileList.map(file => ({
            name: file.name,//文件名
            mtime: file.mtime || '',//修改时间
            size: file.size || '',//文件大小
            sPath: file.path
          }))
        }
      },
      (response: IRes) => {
        if (response.code === 0) {
          callback(response);
          rs();
        } else {
          rj(new Error(`下载失败:${response.msg}`));
        }
      }
    )
  })
}

export const videoWallUserOptions = (): Promise<void> => {
  return new Promise((rs, rj) => {
    try {
      sendDataToApp(
        'videoWall_clickUserIcon',
        {},
        (res: IRes) => {
          if (res.code === 0) {
            rs();
          }
        }
      )
    } catch (e) {
      rj(e);
    }
  })
}

// 判断是否允许投屏
export const videoWallHDMIGet = (): Promise<{ code: number, data: { list: string[] } }> => {
  return new Promise((rs, rj) => {
    try {
      sendDataToApp(
        _Screen_Get,
        {},
        (res: IRes) => {
          if (res.code === 0) {
            rs(res.data);
          }
        }
      )
    } catch (e) {
      rj(e);
    }
  })
}


// 影视墙投屏方法
export const videoWallHDMIOnClick = (): Promise<void> => {
  return new Promise((rs, rj) => {
    try {
      sendDataToApp(
        _Screen_Click,
        {},
        (res: IRes) => {
          if (res.code === 0) {
            rs();
          }
        }
      )
    } catch (e) {
      rj(e);
    }
  })
}