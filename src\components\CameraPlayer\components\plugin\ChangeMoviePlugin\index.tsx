import { PreloadImage } from "@/components/Image";
import Player from "xgplayer/es/player";
import leftArrow from '@/Resources/player/changeMovie/leftArrow.png';
import rightArrow from '@/Resources/player/changeMovie/rightArrow.png';
import "./index.css";
import { useCallback, useMemo, useState } from "react";
import { px2rem } from "@/utils/setRootFontSize";

interface IChangeMoviePlugin {
  deviceType: 'pc' | 'mobile'
  player: React.MutableRefObject<Player | null>
  movieTitle: string
  urls: string[]
  playerType: 'normal' | 'multiple'
  curUrl: string
}

const ChangeMoviePlugin = (props: IChangeMoviePlugin) => {
  const { player, movieTitle, urls, playerType, curUrl } = props;
  const [currentUrl, setCurrentUrl] = useState<string>(curUrl);

  const currentIndex: number = useMemo(() => {
    return urls.findIndex((item) => item === currentUrl);
  }, [currentUrl, urls])

  const onPrev = useCallback(() => {
    if (currentIndex <= 0) return;
    if (player.current) {
      player.current.src = urls[currentIndex - 1];
      setCurrentUrl(urls[currentIndex - 1]);
    }
  }, [currentIndex, player, urls])

  const onNext = useCallback(() => {
    if (currentIndex === urls.length - 1) return;
    if (player.current) {
      player.current.src = urls[currentIndex + 1];
      setCurrentUrl(urls[currentIndex + 1]);
    }
  }, [currentIndex, player, urls])

  return (
    <div className="change_movie_plugin_container">
      <span style={{ display: playerType === 'normal' ? 'none' : 'flex' }} onClick={onPrev} className={`${currentIndex <= 0 ? 'change_movie_plugin_disabled' : ''}`}><PreloadImage className="change_movie_plugin_container_img" src={leftArrow} /></span>
      <span style={{ margin: `0 ${px2rem("10px")}` }}>{movieTitle}</span>
      <span style={{ display: playerType === 'normal' ? 'none' : 'flex' }} onClick={onNext} className={`${currentIndex === urls.length - 1 ? 'change_movie_plugin_disabled' : ''}`}><PreloadImage className="change_movie_plugin_container_img" src={rightArrow} /></span>
    </div>
  )
}

export default ChangeMoviePlugin;