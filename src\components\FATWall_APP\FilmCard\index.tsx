import styles from './index.module.scss';
import { px2rem } from '@/utils/setRootFontSize';
import { useMemo, useRef } from 'react';
import { useHistory } from 'react-router-dom';
import { useInViewport, useUpdateEffect } from 'ahooks';
import { PreloadImage } from '@/components/Image';

export interface IFilmCard {
  media_id?: number;
  file_id?: number;
  poster: string
  isDrama?: boolean // 是否是电视剧的卡片类型
  progress?: number
  title: string
  setNum?: number
  time: string
  os?: string
  options?: {
    style?: React.CSSProperties;
    callback?: () => void
  }
  type?: 'play' | 'add'
  layout?: 'vertical' | 'horizontal'
  // 添加可能需要传递给VideoDetails的其他属性
  score?: number | string
  year?: string
  kind?: any
  category?: string
  duration?: number
  source?: string
  posterUrl?: string
  actors?: { name: string, role: string }[]
  fileInfo?: { path: string, size: string }
  synopsis?: string
  versions?: { id: string, name: string }[]
  file_media_id?: number
  allMediaFiles?: any[] // 完整的剧集文件列表
}

interface IFilmCardList {
  list: IFilmCard[]
  isDrama?: boolean
  type: 'play' | 'add'
  hasMore?: boolean
  setNewPage?: React.Dispatch<React.SetStateAction<{
    offset: number;
    limit: number;
  }>>
}

export const FilmCardList = (props: IFilmCardList) => {
  const { list, isDrama, type, hasMore, setNewPage } = props;
  const divRef = useRef(null);

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(divRef, {
    threshold: 0.2,
  })

  useUpdateEffect(() => {
    if (inViewport) {
      // 滚动到底部，加载更多数据
      setNewPage && setNewPage(prev => ({ ...prev, offset: prev.offset + prev.limit }))
    }
  }, [inViewport, setNewPage])

  return (
    <div className={isDrama ? styles.card_list_container_drama : styles.card_list_container}>
      {
        list.map((item) => (
          <FilmCard
            type={type}
            key={item.file_id || item.media_id || item.title}
            {...item}
            isDrama={isDrama}
            options={
              item.options ||
              (isDrama ? { style: { marginRight: '12px' } } : { style: { width: px2rem('120px'), height: px2rem('80px') } })
            }
          />
        ))
      }
      {
        hasMore && <div ref={divRef} style={{ padding: px2rem('10px') }}></div>
      }
    </div>
  )
}

const FilmCard = (props: IFilmCard) => {
  const { file_media_id, poster, progress, time, title, os, options, type = 'add', layout = 'vertical', isDrama, setNum, allMediaFiles, ...restProps } = props;
  const history = useHistory();

  const typeLabel: string = useMemo(() => {
    switch (type) {
      case 'play': return '观看';
      case 'add': return '添加';
      default: return '观看';
    }
  }, [type])

  // 处理卡片点击事件
  const handleCardClick = () => {
    if (options?.callback) {
      options.callback();
      return;
    }

    // 如果是电视剧模式，
    if (isDrama) {
      const filmInfoData = { file_media_id, title, setNum, poster, progress, time, os, type, ...restProps };
      console.log('FilmCard跳转到SingleEpisode，传递的数据:', {
        filmInfo: filmInfoData,
        poster: poster,
        posterUrl: filmInfoData.posterUrl
      });

      history.push({
        pathname: '/filmAndTelevisionWall_app/all/videoDetails/singleEpisode',
        state: {
          isDrama: true,
          filmInfo: filmInfoData,
          allMediaFiles: allMediaFiles // 传递完整的剧集列表
        }
      });
      return;
    }

    // 跳转到VideoDetails页面，传递相关信息
    const filmInfo = {
      title,
      poster,
      progress,
      time,
      os,
      type,
      ...restProps
    };

    if (layout === 'horizontal') {
      // 将参数拼接到URL上，以便webView能够正确获取
      const params = new URLSearchParams({
        classes: filmInfo.category || '',
        media_id: filmInfo.media_id?.toString() || '0',
        lib_id: '0',
        isDrama: 'false'
      });

      history.push(`/filmAndTelevisionWall_app/all/videoDetails?${params.toString()}`);
    }

  };

  if (layout === 'horizontal') {
    return (
      <div className={styles.horizontal_container}>
        <div className={styles.content_container} style={{ ...options?.style }} onClick={handleCardClick} >
          <PreloadImage style={{ width: '100%', height: '100%', borderRadius: px2rem('12px') }} src={poster} alt='poster' />
          {
            progress && progress !== 0 ? (
              <div className={styles.progress}>
                <div className={styles.content} style={{ width: `${progress}%` }}></div>
              </div>
            ) : <></>
          }
        </div>
        <div className={styles.horizontal_text_container}  style={{ width: 180 }}>
          <span className={styles.text_container_title} onClick={handleCardClick} title={title}>{title}</span>
          {time !== '' && <span className={styles.text_container_subtitle} onClick={handleCardClick}>{`${time}前${os ? '在' + os : ''}${typeLabel}`}</span>}
        </div>
      </div>
    )
  }

  return (
    <div className={isDrama ? styles.container_drama : styles.container} onClick={isDrama ? handleCardClick : undefined}>
      <div className={isDrama ? styles.content_container_drama : styles.content_container} onClick={!isDrama ? handleCardClick : undefined}
        style={{ ...options?.style }}>
        <PreloadImage style={{ width: '100%', height: '100%', borderRadius: px2rem('12px') }} src={poster} alt='poster' />
        {
          progress && progress !== 0 ? (
            <div className={styles.progress} style={isDrama ? { height: '2px', bottom: '8px', background: 'transparent', width: '80%', } : {}}>
              <div className={styles.content} style={{
                width: `${progress}%`,
                ...(isDrama ? {
                  background: 'rgba(89, 156, 250, 1)',
                  height: '4px',
                  position: 'absolute',
                  bottom: '0'
                } : {})
              }}></div>
            </div>
          ) : <></>
        }
      </div>
      {!isDrama && (
        <div className={styles.text_container} style={{ width: options?.style?.width }}>
          <span className={styles.text_container_title} onClick={handleCardClick} title={title}>{title}</span>
          {time !== '' && <span className={styles.text_container_subtitle} onClick={handleCardClick}>{`${time}前${os ? '在' + os : ''}${typeLabel}`}</span>}
        </div>
      )}
      {isDrama && (
        <div className={styles.text_container} style={{ width: options?.style?.width }}>
          <span className={styles.text_container_title_drama} onClick={handleCardClick}>{'第' + setNum + '集'}</span>
        </div>
      )}
    </div>
  )
}

export default FilmCard;