.matchModal {
  :global(.ant-modal-content) {
    padding: 0;
    border-radius: 32px;
    overflow: hidden;
    background-color: var(--desktop-modal-bg-color);
  }
  :global {
    .ant-modal-title,
    .ant-modal .ant-modal-header {
      background-color: var(--desktop-modal-bg-color);
    }
    .ant-input::placeholder {
      color: var(--correction-disable-title-color)
    }
  }
}
.matchModalSearch {
  :global(.ant-modal-content) {
    padding: 0;
    border-radius: 32px;
    overflow: hidden;
    height: 636px;
    width: 862px;
    background-color: var(--desktop-modal-bg-color);
  }
}
.modalHeader {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 55px;
  position: relative;
  background-color: var(--desktop-modal-bg-color);
}

.backIcon {
  position: absolute;
  left: 20px;
  cursor: pointer;
  width: 40px;
  height: 40px;
}

.title {
  font-size: 20px;
  font-weight: 500;
  color: var(--title-color);
}

.modalWrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.searchContainer {
  // margin-top: 15px;
  padding: 10px 20px 16px;
  display: flex;
  align-items: center;
}
.searchContainerTwo {
  padding: 10px 20px 16px;
  margin-top: 15px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.searchBox {
  flex: 1;
  height: 40px;
  background-color: var(--event-card-background-color);
  border-radius: 20px;
  display: flex;
  align-items: center;
}

.searchIcon {
  color: var(--list-value-text-color);
  margin-right: 8px;
}

.searchInput {
  flex: 1;
  border: none;
  background: transparent;
  height: 100%;
  font-size: 14px;
  outline: none;
  width: 80%;
  color: var(--subtitle-text-color);
}

.searchInput::placeholder {
  color: var(--subtitle-text-color);
}

.clearButton {
  cursor: pointer;
  color: var(--list-value-text-color);
  display: flex;
  align-items: center;
  justify-content: center;
}

.searchResults {
  flex: 1;
  overflow-y: auto;
  padding: 0 30px 0 24px;
}

.noResults {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--list-value-text-color);
}

.noResultsIcon {
  width: 100px;
  height: 64px;
  margin-bottom: 16px;
}

.noResultsText {
  font-size: 14px;
  color: var(--list-value-text-color);
}

.retryButton {
  margin-top: 16px;
  color: var(--primary-color);
  cursor: pointer;
}

.resultItem {
  display: flex;
  padding: 6px 0;
  cursor: pointer;
}

.resultItem:last-child {
  border-bottom: none;
}

.resultPoster {
  width: 70px;
  height: 105px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;
}

.resultPoster img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.resultInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.resultTitle {
  font-size: 16px;
  font-weight: 500;
  color: var(--primary-color);
}

.resultMeta {
  font: MiSans;
  font-size: 12px;
  color: var(--list-value-text-color);
}

// .resultMeta span {
//   color: var(--subtitle-text-color);
// }

.resultSelect {
  display: flex;
  align-items: center;
  margin-left: 12px;
}

.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.footerActions {
  padding: 16px 20px 24px;
  display: flex;
  justify-content: center;
}

.confirmButton {
  width: 100%;
  height: 50px;
  border-radius: 20px;
  font-size: 16px;
  background-color: var(--primary-color);
  color: #fff;
  border: none;
  cursor: pointer;
  transition: all 0.3s;
  max-width: 336px;
}

.confirmButton:disabled {
  background-color: var(--correction-disable-btn-color);
  // opacity: 0.6;
  color: var(--correction-disable-title-color);
  cursor: not-allowed;
  height: 50px;
  max-width: 336px;
}

.modalFooter {
  display: flex;
  background-color: #f3f5f6;
  padding: 10px;
  width: 50%;
  margin-bottom: 20px;
  margin-left: 20px;
  border-radius: 20px;
  justify-content: space-between;

  > div {
    // margin-right: 10px;
    display: flex;
    gap: 5px;
    // margin-left: 10px;
    padding-right: 15px;
    border-right: 1px solid var(--thinLine-background-color);
    color: #5c6167;
  }
  > div:last-child {
    border-right: none;
  }
}

.resultItem {
  display: flex;
  align-items: center;
  // justify-content: ;
  flex-wrap: wrap;
  gap: 28px;
}

// 添加选中项目的高亮样式
.selectedItem {
  position: relative;

  &::after {
    content: "";
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    pointer-events: none;
    z-index: 1;
  }
}

.customRadio {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--cancel-btn-background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  &.selected {
    background-color: var(--primary-color);
    border-color: var(--primary-color);

    .innerDot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #fff;
    }
  }
}
