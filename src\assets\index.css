
.testBox {
    width: 100px;
    height: 100px;
    background-color: pink;
}
#loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(250, 250, 250, 0.65);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    z-index: 2000;
}

/* 仅显示图标的loading样式，无遮罩层 */
.loading-icon-only {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    right: auto !important;
    bottom: auto !important;
    transform: translate(-50%, -50%) !important;
    background: transparent !important;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    z-index: 2000;
    width: auto !important;
    height: auto !important;
}
.path{
    margin-left: 15px;
}
.adm-image{
    margin-top: 10px;
}