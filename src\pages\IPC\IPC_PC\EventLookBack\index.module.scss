.container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

.header {
  height: 60px;
  font-family: MiSans;
  font-weight: 500;
  font-size: 20px;
  letter-spacing: 0px;
  color: var(--text-color);
  background-color: var(--background-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.operation {
  font-family: MiSans;
  font-weight: 500;
  font-size: 17px;
  line-height: 100%;
  letter-spacing: 0px;
  vertical-align: middle;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 16px;
  height: 60px;

  :global {
    .ant-picker {
      color: var(--subtitle-text-color);
      background: var(--background-color) !important;
    }
    .ant-picker-range {
      background: var(--background-color) !important;
    }
    .ant-picker-outlined {
      background: var(--background-color) !important;
    }
    .ant-picker-separator,
    .ant-picker-clear,
    .ant-picker-suffix {
      color: var(--subtitle-text-color) !important;
    }
    .ant-picker-input {
      > input {
        text-align: center;
        color: var(--subtitle-text-color) !important;
      }
    }
  }

  .operationItem {
    margin: 0 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px 12px;
    cursor: pointer;
    color: var(--subtitle-text-color);

    & {
      border: 1px solid var(--list-value-text-color);
      border-radius: 6px;
    }
  }

  img {
    width: 20px;
    height: 20px;
  }
}

.operationItem_datePicker {
  height: 32px;
  margin: 0 8px;
}

.content {
  margin-top: 20px;
  flex: 1;

  :global {
    .ant-table-thead > tr > th {
      border: 0;
      background-color: var(--background-color) !important;
      color: var(--system-text-color);
    }
    .ant-table-tbody > tr > td {
      border: 0;
      padding: 4px 12px;
    }
    .ant-table-cell {
      text-align: center !important;
      &::before {
        background-color: transparent !important;
      }
    }
    .ant-table-cell-row-hover {
      background: var(--table-row-hover-color) !important;
    }
    .ant-table-wrapper,
    .ant-table {
      background: transparent !important;
      color: var(--text-color);
    }
  }
}

.urlContainer {
  display: flex;
  cursor: pointer;
}

.tableImage {
  img {
    width: 100px;
    height: 64px;
  }
}

.selectIcon {
  cursor: pointer;
  width: 30px;
  height: 30px;

  img {
    width: 28px;
    width: 28px;
  }
}

.hoverImage {
  width: 30px;
  height: 30px;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: calc(50% - 15px);
  left: calc(50% - 15px);

  img {
    width: 24px;
    width: 24px;
  }
}

.emptyState {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.emptyText {
  color: var(--text-color);
}
