.folderSelectModal {
  :global {
    .ant-modal-content {
      border-radius: 32px;
      overflow: hidden;
      background-color: var(--desktop-modal-bg-color);
    }

    .ant-modal-header {
      padding: 0;
      margin-bottom: 0;
      border-bottom: none;
    }

    .ant-modal-title {
      padding: 0;
      margin: 0;
      line-height: 1;
    }

    .ant-modal-body {
      padding: 0;
    }

    .ant-checkbox-checked .ant-checkbox-inner {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
    }
  }
}

.modalHeader {
  display: flex;
  align-items: center;
  padding: 16px 10px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--desktop-modal-bg-color);

  .backIcon {
    font-size: 18px;
    color: var(--text-color);
    cursor: pointer;
    margin-right: 16px;
  }

  .modalTitle {
    flex: 1;
    text-align: center;
    font-size: 18px;
    font-weight: 500;
    color: var(--text-color);
    margin-right: 34px; /* 平衡左侧返回按钮的宽度，使标题居中 */
  }
}

.modalContent {
  display: flex;
  flex-direction: column;
  height: calc(636px - 90px);
}

.folderSelectHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 10px;
  border-bottom: 1px solid var(--border-color);

  .folderTag {
    display: inline-block;
    background-color: rgba(255, 152, 0, 0.1);
    color: #ff9800;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 14px;
  }
}

.fileListContainer {
  flex: 1;
  overflow-y: auto;
  padding: 0 10px;
}

.fileList {
  width: 100%;
  :global {
    .ant-list-item {
      padding: 12px 24px;
      border-bottom: 1px solid var(--border-color);

      &:hover {
        background-color: var(--hover-background-color);
      }
    }
  }
}

.fileItem {
  display: flex;
  align-items: center;
  justify-content: space-between; // 添加这个属性让内容分布在两端

  // &.selectedItem {
  //   background-color: var(
  //     --item-selected-background-color,
  //     rgba(0, 0, 0, 0.05)
  //   );
  // }
}

.checkboxCell {
  margin-left: 12px; // 改为左边距，因为现在在右侧

  .checkbox {
    display: flex;
    align-items: center;
    justify-content: center;
    :global {
      .ant-checkbox .ant-checkbox-inner {
        background-color: var(--checkbox-bg-color);
        border-color: var(--checkbox-bg-color);
      }
      .ant-checkbox-checked .ant-checkbox-inner {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
      }
    }
  }
}

.fileContent {
  display: flex;
  align-items: center;
  flex: 1;
}

.fileIcon {
  width: 40px;
  height: 40px;
  margin-right: 12px;
}

.fileInfo {
  flex: 1;
}

.fileName {
  font-family: MiSans W;
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  color: var(--text-color);
  margin-bottom: 4px;
}

.fileDetails {
  font-size: 12px;
  color: var(--list-value-text-color);
}

.loadingContainer,
.emptyContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.footerContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
}

.uploadPathContainer {
  flex: 1;
  margin-right: 16px;
  position: relative;
  max-width: calc(100% - 175px - 16px); /* 减去确定按钮宽度和间距 */
  :global {
    .ant-btn-variant-outlined:not(
        :disabled
      ):not(.ant-btn-disabled):hover {
      background-color: var(--card-active-background-color);
    }
  }
}

.uploadPathButton {
  width: 100%;
  text-align: left;
  padding: 14px 20px;
  height: auto;
  color: var(--primary-color);
  border-radius: 20px;
  border-color: var(--primary-color);
  display: flex;
  align-items: center;
  font-weight: 500;
  background-color: var(--card-active-background-color);
}

.pathLabel {
  flex-shrink: 0;
  white-space: nowrap;
}

.pathContainer {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0; /* 重要：使flex子元素可以收缩到比内容小 */
}

.uploadPath {
  color: var(--primary-color);
  margin-left: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.vipTip {
  position: absolute;
  right: 0;
  bottom: 36px;
  transform: translateX(-50%);
  background-color: #402c00;
  color: #e2ae1e;
  padding: 2px 5px;
  border-radius: 5px;
  font-size: 12px;
  border: 0.1px solid #e2ae1e;
  z-index: 10;
}
.uploadButton {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  height: 50px;
  min-width: 175px;
  border-radius: 20px;

  &:disabled {
    background-color: var(--primary-color);
    color: rgba(255, 255, 255, 1);
    border: none;
    opacity: 0.3;
  }
}
