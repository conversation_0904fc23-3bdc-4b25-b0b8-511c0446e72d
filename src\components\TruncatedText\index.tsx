import React, { useEffect, useRef, useState, useCallback } from 'react';
import styles from './index.module.scss';

interface TruncatedTextProps {
  text: string;
  className?: string;
  style?: React.CSSProperties;
  maxLines?: number;
  ellipsis?: string;
  maxEndLength?: number;
}

const TruncatedText: React.FC<TruncatedTextProps> = ({
  text,
  className = '',
  style = {},
  maxLines = 2,
  ellipsis = '...',
  maxEndLength = 10
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [displayText, setDisplayText] = useState<string | JSX.Element>(text);
  const [needsTruncation, setNeedsTruncation] = useState(false);

  const measureTextWidth = useCallback((text: string, font: string): number => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return 0;
    ctx.font = font;
    return ctx.measureText(text).width;
  }, []);

  const truncateText = useCallback(() => {
    if (!containerRef.current || !text) return;

    const container = containerRef.current;
    const originalText = text.trim();

    // 重置容器内容以获取准确的样式信息
    container.innerHTML = originalText;

    const computedStyle = window.getComputedStyle(container);
    const font = `${computedStyle.fontSize} ${computedStyle.fontFamily}`;
    const padding = parseFloat(computedStyle.paddingLeft) + parseFloat(computedStyle.paddingRight);
    const availableWidth = container.clientWidth - padding - 10;

    // 计算第一行可容纳的文本
    let firstLine = '';
    for (let i = 1; i <= originalText.length; i++) {
      const sample = originalText.substring(0, i);
      if (measureTextWidth(sample, font) <= availableWidth) {
        firstLine = sample;
      } else {
        break;
      }
    }

    const remainingText = originalText.substring(firstLine.length);

    // 如果没有剩余文本，直接显示原文
    if (remainingText.length === 0) {
      setDisplayText(originalText);
      setNeedsTruncation(false);
      return;
    }

    const ellipsisWidth = measureTextWidth(ellipsis, font);
    let splitResult = null;

    // 尝试分割第二行
    let endPartLength = Math.min(remainingText.length, maxEndLength);

    while (endPartLength > 0) {
      const endPart = remainingText.slice(-endPartLength);
      const endPlusEllipsisWidth = measureTextWidth(ellipsis + endPart, font);
      const availableMiddleWidth = availableWidth - endPlusEllipsisWidth;

      let middlePart = '';
      for (let i = 1; i <= remainingText.length - endPartLength; i++) {
        const sample = remainingText.substring(0, i);
        if (measureTextWidth(sample, font) <= availableMiddleWidth) {
          middlePart = sample;
        } else {
          break;
        }
      }

      // 检查是否需要截断
      if (middlePart.length + endPartLength < remainingText.length) {
        splitResult = { middle: middlePart, end: endPart };
        break;
      }

      endPartLength--;
    }

    if (splitResult) {
      setDisplayText(
        <>
          <div>{firstLine}</div>
          <div>{splitResult.middle}{ellipsis}{splitResult.end}</div>
        </>
      );
      setNeedsTruncation(true);
    } else {
      console.log('回退：使用原文本，让CSS处理省略');

      // 回退：使用原文本，让CSS处理省略
      setDisplayText(originalText);
      setNeedsTruncation(false);
    }
  }, [text, ellipsis, maxEndLength, measureTextWidth]);

  // 监听容器大小变化
  useEffect(() => {
    if (!containerRef.current) return;

    const resizeObserver = new ResizeObserver(() => {
      truncateText();
    });

    resizeObserver.observe(containerRef.current);

    // 初始计算
    truncateText();

    return () => {
      resizeObserver.disconnect();
    };
  }, [truncateText]);

  // 监听文本变化
  useEffect(() => {
    truncateText();
  }, [text, truncateText]);

  return (
    <>
      <div
        className={`${styles.textContainer} ${className}`}
        style={{
          ...style,
          ...(needsTruncation ? {} : {
            display: '-webkit-box',
            WebkitBoxOrient: 'vertical',
            WebkitLineClamp: maxLines,
            overflow: 'hidden',
          })
        }}
      >
        <div ref={containerRef} className={`${styles.textContainer} ${className} ${styles.hidden}`}></div>
        {displayText}
      </div>
    </>

  );
};

export default TruncatedText;
