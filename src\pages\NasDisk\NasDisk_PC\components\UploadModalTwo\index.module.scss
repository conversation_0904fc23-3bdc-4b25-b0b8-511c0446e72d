.uploadModal {
  :global {
    .ant-modal-content {
      border-radius: 32px;
      overflow: hidden;
      background-color: var(--desktop-modal-bg-color);
    }

    .ant-modal-header {
      padding: 0;
      margin-bottom: 0;
      border-bottom: none;
    }

    .ant-modal-title {
      padding: 0;
      margin: 0;
      line-height: 1;
    }

    .ant-modal-body {
      padding: 0;
    }

    .ant-checkbox-checked .ant-checkbox-inner {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
    }

    .ant-checkbox-disabled .ant-checkbox-inner {
      background-color: rgba(153, 153, 153, 0.3);
      border-color: rgba(153, 153, 153, 0.3);
    }
  }
}

.modalHeader {
  display: flex;
  align-items: center;
  padding: 16px 10px;
  background-color: var(--desktop-modal-bg-color);
  border-bottom: 1px solid var(--border-color);

  .backIcon {
    font-size: 18px;
    color: var(--text-color);
    cursor: pointer;
    margin-right: 16px;
  }

  .modalTitle {
    flex: 1;
    text-align: center;
    font-size: 18px;
    font-weight: 500;
    color: var(--text-color);
    margin-right: 34px;
    /* 平衡左侧返回按钮的宽度，使标题居中 */
  }
}

.breadcrumbHeader {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 16px 0px;
  border-bottom: 1px solid var(--border-color);
}

.breadcrumbContainer {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .breadcrumbItem {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 14px;

    &.breadcrumbCurrent {
      color: rgba(255, 178, 29, 1);
      background-color: rgba(255, 178, 29, 0.15);
    }
  }
}

.modalContent {
  display: flex;
  flex-direction: column;
  height: 100%;

  .fileListContainer {
    flex: 1;
    overflow-y: auto;
    padding: 0;

    .fileList {
      width: 100%;

      :global {
        .ant-list-item {
          padding: 12px 24px;
          border-bottom: 1px solid var(--border-color);

          &:hover {
            background-color: var(--hover-background-color);
          }
        }
      }

      .fileItem {
        display: flex;
        align-items: center;

        &.selectedItem {
          background-color: var(--item-selected-background-color,
              rgba(0, 0, 0, 0.05));
        }

        .checkboxCell {
          margin-right: 12px;

          :global {
            .ant-checkbox .ant-checkbox-inner {
              background-color: var(--checkbox-bg-color);
              border-color: var(--checkbox-bg-color);
            }

            .ant-checkbox-checked .ant-checkbox-inner {
              background-color: var(--primary-color);
              border-color: var(--primary-color);
            }
          }
        }

        .fileContent {
          display: flex;
          align-items: center;
          flex: 1;
          cursor: pointer;
          transition: all 0.2s;

          &.selectedContent {
            .fileName {
              color: var(--primary-color);
              font-weight: 600;
            }
          }

          &:hover {
            .fileName {
              color: var(--primary-color);
            }
          }

          .fileIcon {
            width: 36px;
            height: 36px;
            margin-right: 12px;
          }

          .fileInfo {
            flex: 1;

            .fileName {
              font-family: MiSans W;
              font-weight: 500;
              font-size: 16px;
              line-height: 22px;
              color: var(--text-color);
              margin-bottom: 4px;
            }

            .fileDetails {
              font-size: 12px;
              color: var(--list-value-text-color);
            }
          }
        }
      }
    }

    .loadingContainer {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: var(---list-value-text-color);
    }

    .emptyContainer {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;

      .emptyText {
        color: var(--list-value-text-color);
        font-size: 14px;
      }
    }
  }

  .footerContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0px;
    border-top: 1px solid var(--border-color);
    gap: 16px;

    .selectionInfo {
      font-size: 14px;
      color: var(--text-secondary-color);
      min-width: 80px;
    }

    .uploadPathContainer {
      flex: 1;
      max-width: 300px;
      /* 固定宽度，可根据需求调整 */
      overflow: hidden;
      /* 防止内容溢出容器 */

      .uploadPathButton {
        width: 100%;
        text-align: left;
        padding: 14px 20px;
        height: auto;
        color: var(--primary-color);
        border-radius: 20px;
        display: flex;
        align-items: center;
        font-weight: 500;
        border-color: var(--primary-color);
        background-color: var(--card-active-background-color);

        .uploadPath {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          color: var(--primary-color);
        }
      }
    }

    .uploadButton {
      background-color: var(--primary-color);
      border-color: var(--primary-color);
      height: 50px;
      min-width: 175px;
      border-radius: 20px;

      &:disabled {
        background-color: rgba(153, 153, 153, 1);
        color: rgba(255, 255, 255, 1);
        border: none;
      }
    }
  }
}