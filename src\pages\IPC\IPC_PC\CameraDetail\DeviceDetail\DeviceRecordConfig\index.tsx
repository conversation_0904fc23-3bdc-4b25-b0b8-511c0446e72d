import List, { IListData, modalShow } from "@/components/List";
import baseStyles from "../index.module.scss";
import { Divider, Form, FormInstance } from "antd";
import { useCallback, useEffect, useMemo, useState } from "react";
import { eventTypeList } from "@/components/CameraPlayer/constants";
import { PreloadImage } from "@/components/Image";
import next from "@/Resources/modal/next.png";
import next_dark from "@/Resources/modal/next_dark.png";
import dataSelect from "@/Resources/player/dateSelect.png";
import listStyles from "@/components/List/index.module.scss";
import EventListCard from "@/components/CameraPlayer/components/EventList/EventListCard";
import { useTheme } from "@/utils/themeDetector";
import { IDeviceDetail } from "..";
import RecordPlanPC from "./RecordPlan";
import { useRequest } from 'ahooks';
import { EventTrigger, RecordConfig, RecordSchedule, setupRecordCamera } from "@/api/ipc";
import { ICollapsePanel } from "@/layouts/Layout";
import { ICameraDetail } from "@/pages/IPC/IPC_APP/CameraDetail";
import styles from './index.module.scss';
import PopoverSelector from "@/components/PopoverSelector";
import { Toast } from "@/components/Toast/manager";
import Modal from "@/components/Modal";
import { px2rem } from "@/utils/setRootFontSize";

export interface IDeviceRecordConfig {
  saveURl: string,
  isDynamicBackup: boolean,
  videoDuration: number,
  videoSize: number,
  capacityShortageHandlingStrategy: string,
  capacityWarningThreshold: string,
  recordPlan: RecordSchedule,
  recordModel: string,
  source: string,
  eventBeforeDuration: number,
  eventAfterDuration: number,
  checkEvent: EventTrigger[],
  internetTimeServer: string,
  videoSizeUnit?: string
}
const sensitivityOptions = [
  { label: '低灵敏度', value: '0' },
  { label: '中灵敏度', value: '1' },
  { label: '高灵敏度', value: '2' },
]

const SensitivityItem = (props: { form: FormInstance<any>, name: string, isDarkMode: boolean, sensitivity: string }) => {
  const { form, name, sensitivity } = props;
  const [visible, setVisible] = useState<boolean>(false); // 灵敏度选择器
  const [curValue, setCurValue] = useState<string>(sensitivity); // 灵敏度选择值

  const optionsOnChange = useCallback((value) => {
    setCurValue(value);
    form.setFieldValue(name, value);
  }, [form, name])

  return (
    <div className={listStyles.listItemContainer}>
      <span className={styles.listItem_label}>{'灵敏度'}</span>
      <span className={styles.listItem_value}>
        <PopoverSelector visible={visible} onVisibleChange={setVisible} onChange={optionsOnChange} value={curValue} options={sensitivityOptions}>
          <div className={`${styles.listItem_value_container} ${listStyles.onClick}`}>
            {sensitivityOptions.find((item) => item.value === curValue)?.label}
            <PreloadImage src={dataSelect} alt="select" />
          </div>
        </PopoverSelector>
      </span>
    </div>
  )
}

const DeviceRecordConfig = (props: { cameraId?: string, curDevice?: (ICollapsePanel & ICameraDetail & IDeviceDetail), setCurDevice: (v: (p: any) => any) => void, onDestroy: () => void; }) => {
  const { cameraId, curDevice, setCurDevice } = props;
  const [form] = Form.useForm();
  const { isDarkMode } = useTheme();
  const data = useMemo(() => {
    return curDevice?.data.recordConfig as IDeviceRecordConfig;
  }, [curDevice?.data.recordConfig])

  // 录制计划是否显示
  const [recordPlayShow, setRecordPlayShow] = useState<boolean>(false);

  // 设置IPC本地录制属性hook
  const { runAsync } = useRequest(setupRecordCamera, { manual: true });

  // 统一hook的回调
  const setRecordHookCallback = useCallback(async (callback: () => void, params: RecordConfig) => {
    if (!cameraId) return;
    try {
      const res = await runAsync({ camera: [cameraId], config: params });
      if (res && res.code === 0) {
        callback();
      }
    } catch (e) {
      console.log(e);
      Toast.show('修改失败！');
    }
  }, [cameraId, runAsync])

  // 动态备份的回调
  const switchCallback = useCallback(async (checked) => {
    checked ? window.onetrack?.('track', 'ipc_recordConfig_backup_enable') : window.onetrack?.('track', 'ipc_recordConfig_backup_disable');
    setRecordHookCallback(() => {
      setCurDevice((prev: IDeviceDetail) => {
        let p: IDeviceDetail = { ...prev };
        p!.data.recordConfig['isDynamicBackup'] = checked;
        return p;
      })
    }, { backup: checked });
  }, [setCurDevice, setRecordHookCallback])

  // 通用属性回调
  const handleOnChange = useCallback((value, key, paramName) => {
    setRecordHookCallback(() => {
      setCurDevice((prev: IDeviceDetail) => {
        let p: any = { ...prev };
        p!.data.recordConfig[key] = value;
        return p;
      })
    }, { [paramName]: value });
  }, [setCurDevice, setRecordHookCallback])

  useEffect(() => {
    console.log(curDevice, '被修改了!');
  }, [curDevice])

  // 存档容量回调
  const videoSizeOnChange = useCallback((value) => {
    const space_limit = value.input * (value.unit === 'GB' ? 1024 : value.unit === 'MB' ? 1 : 1 / 1024);
    setRecordHookCallback(() => {
      setCurDevice((prev: IDeviceDetail) => {
        let p: IDeviceDetail = { ...prev };
        p!.data.recordConfig['videoSize'] = value.input;
        p!.data.recordConfig['videoSizeUnit'] = value.unit;
        return p;
      })
    }, { space_limit: space_limit });
  }, [setCurDevice, setRecordHookCallback])

  // 检测事件弹窗
  const showCheckEvent = useCallback((events: EventTrigger[]) => {
    let initValue: { [key: string]: boolean } = {}
    if (events.filter((it) => it.enabled).length === eventTypeList.length) {
      initValue = {
        move: true,
        human: true,
        fire: true,
        pet: true,
        noise: true,
      }
    } else {
      events.forEach((it) => {
        initValue[it.name] = it.enabled;
      })
    }
    modalShow('检测事件', <Form form={form} initialValues={{ ...initValue }}>
      {
        eventTypeList.map((item, index) => {
          return item.type !== 'move' ? <Form.Item noStyle key={item.type} name={item.type} valuePropName="checked">
            <EventListCard icon={item.icon} title={item.label} subtitle={item.describe} rightOpt={{ type: 'switch', title: item.type }} />
          </Form.Item> :
            <div key={item.type + index}>
              <Form.Item noStyle key={item.type} name={item.type} valuePropName="checked">
                <EventListCard icon={item.icon} title={item.label} subtitle={item.describe} rightOpt={{ type: 'switch', title: item.type }} />
              </Form.Item>
              <Form.Item noStyle name={item.type + 'Sensitivity'}>
                <SensitivityItem name={item.type + 'Sensitivity'} form={form} isDarkMode={isDarkMode} sensitivity={String(data.checkEvent.find((it) => it.name === item.type)?.sensitivity) || '1'} />
              </Form.Item>
            </div>
        })
      }
    </Form>, (m) => {
      form.validateFields().then((value) => {
        let checkEventParam: EventTrigger[] = []; // 接口中的检测事件参数
        Object.keys(value).forEach((key) => {
          if (key === 'moveSensitivity') return;
          if (key === 'move') {
            checkEventParam.push({ name: key, enabled: value[key], sensitivity: Number(value[key + 'Sensitivity']) });
            return;
          }
          checkEventParam.push({
            name: key, enabled: value[key], sensitivity: 1
          })
        })
        const enableList: { [key: string]: any } = {};
        const disableList: { [key: string]: any } = {};
        checkEventParam.forEach(it => {
          if (it.enabled) {
            enableList[it.name] = it.enabled;
          } else {
            disableList[it.name] = it.enabled;
          }
        })
        window.onetrack?.('track', 'ipc_recordConfig_event_trigger_enable', enableList);
        window.onetrack?.('track', 'ipc_recordConfig_event_trigger_disable', disableList);
        setRecordHookCallback(() => {
          setCurDevice((prev: IDeviceDetail) => {
            let p: IDeviceDetail = { ...prev };
            p!.data.recordConfig['checkEvent'] = checkEventParam
            return p;
          })
          m.destroy();
        }, { event_trigger: checkEventParam });
      }).catch(() => null)
    }, () => form.resetFields(), false, { backTheme: isDarkMode ? 'dark' : 'light', position: 'center' })
  }, [data.checkEvent, setCurDevice, form, isDarkMode, setRecordHookCallback])

  const saveConfigDataSource: IListData[] = useMemo(() => {
    const videoSizeTransform = (size: number | undefined, unit: string | undefined) => {
      if (!size) return;
      if (!unit) unit = 'MB';
      return `${size}${unit}`;
    }
    const videoSize = videoSizeTransform(data?.videoSize, data?.videoSizeUnit);
    return [
      { key: 'saveURl', type: 'text', label: '存放位置', value: data?.saveURl },
      { key: 'isDynamicBackup', type: 'switch', label: '动态备份', value: data?.isDynamicBackup, onCallback: switchCallback },
      {
        key: 'videoDuration', type: 'select', label: '录像保存时长', value: String(data?.videoDuration), options: {
          selectOptions: {
            options: [
              { label: '7天', value: '7' },
              { label: '14天', value: '14' },
              { label: '30天', value: '30' },
              { label: '90天', value: '90' },
              { label: '365天', value: '365' },
              { label: '无限制', value: '0' },
            ]
          }
        },
        onCallback: (v) => {
          window.onetrack?.('track', 'ipc_recordConfig_retentionPeriod_count', { retentionPeriod: v });
          setRecordHookCallback(() => {
            setCurDevice((prev: IDeviceDetail) => {
              let p: any = { ...prev };
              p!.data.recordConfig['videoDuration'] = Number(v);
              return p;
            })
          }, { retention_period: v });
        }
      },
      {
        key: 'videoSize', type: 'input', label: '录像存档容量', value: videoSize, options: {
          inputOptions: {
            isNumber: true, units: [
              { label: 'GB', value: 'GB' },
              { label: 'MB', value: 'MB' },
              { label: 'KB', value: 'KB' },
            ]
          }
        },
        onCallback: videoSizeOnChange
      },
      {
        key: 'capacityShortageHandlingStrategy', type: 'select', label: '容量不足处理策略', value: data?.capacityShortageHandlingStrategy, options: {
          selectOptions: {
            options: [
              { label: '停止录制', value: 'stop' },
              { label: '滚动覆盖', value: 'replace' },
            ]
          }
        },
        onCallback: (v) => handleOnChange(v, 'capacityShortageHandlingStrategy', 'space_limit_policy')
      },
      {
        key: 'capacityWarningThreshold', type: 'text', label: '容量预警阈值', value: data?.capacityWarningThreshold, options: {
          inputOptions: {
            isNumber: true, max: 100,
            inputRules: [{
              required: true,
              message: '请输入阈值！'
            }, () => ({
              validator(_, value) {
                if (value > 100 || value < 0) {
                  return Promise.reject(new Error('阈值设置不正确，请输入0-100'));
                }
                return Promise.resolve();
              }
            })]
          }
        },
        // onCallback: capacityWarningThresholdOnChange
      },
    ]
  }, [data?.videoSize, data?.videoSizeUnit, data?.saveURl, data?.isDynamicBackup, data?.videoDuration, data?.capacityShortageHandlingStrategy, data?.capacityWarningThreshold, switchCallback, videoSizeOnChange, setRecordHookCallback, setCurDevice, handleOnChange])

  const recordConfigDataSource: IListData[] = useMemo(() => {
    return [
      {
        key: 'recordPlan', type: 'modal', label: '录制计划', value: data?.recordPlan, render: (t, v, f) => {
          return <div className={`${listStyles.listItem_value_container} ${listStyles.onClick}`} onClick={() => setRecordPlayShow(true)}>
            {v.type === 'full_time' ? '全时段' : '自定义时段'}
            <PreloadImage src={isDarkMode ? next_dark : next} alt="next" />
          </div>
        }
      },
      {
        key: 'recordModel', type: 'select', label: '录制模式', value: data?.recordModel, options: {
          selectOptions: {
            options: [
              { label: '事件触发', value: 'event' },
              { label: '连续录制', value: 'continuous' },
            ]
          }
        },
        onCallback: (v) => handleOnChange(v, 'recordModel', 'record_mode')
      },
      {
        key: 'source', type: 'select', label: '检测来源', value: data?.source, options: {
          selectOptions: {
            options: [
              { label: '摄像机', value: 'camera' },
              { label: '智能存储', value: 'nas' },
            ]
          }
        },
        onCallback: (v) => handleOnChange(v, 'source', 'event_source')
      },
      {
        key: 'checkEvent', type: 'modal', label: '检测事件', value: data?.checkEvent, render: (t, v, f) => {
          return <div className={`${listStyles.listItem_value_container} ${listStyles.onClick}`} onClick={() => showCheckEvent(v)}>
            {`${v.filter((it: any) => it.enabled).length}项`}
            <PreloadImage src={isDarkMode ? next_dark : next} alt="next" />
          </div>
        }
      },
      // { key: 'eventBeforeDuration', type: 'text', label: '事件前录制时长', value: `${data?.eventBeforeDuration}秒` },
      // { key: 'eventAfterDuration', type: 'text', label: '事件后录制时长', value: `${data?.eventAfterDuration}秒` },
    ]
  }, [data?.checkEvent, data?.recordModel, data?.recordPlan, data?.source, handleOnChange, isDarkMode, showCheckEvent])

  // const timeConfigDataSource: IListData[] = useMemo(() => {
  //   return [
  //     {
  //       key: 'internetTimeServer', type: 'select', label: '网络时间服务器', value: data?.internetTimeServer, options: {
  //         selectOptions: {
  //           options: [
  //             { label: '摄像机', value: 'camera' },
  //             { label: '智能存储', value: 'nas' },
  //           ]
  //         }
  //       },
  //       onCallback: (v) => handleOnChange(v, 'internetTimeServer', 'ntp_source')
  //     },
  //   ]
  // }, [data?.internetTimeServer, handleOnChange])

  useEffect(() => {
    window.onetrack?.('track', 'ipc_recordConfig_expose')
  }, [])

  return (
    <div className={baseStyles.right}>
      <span className={baseStyles.right_spanText}>存档</span>
      <List dataSource={saveConfigDataSource} />
      <Divider />
      <span className={baseStyles.right_spanText}>录制</span>
      <List dataSource={recordConfigDataSource} />
      {/* <Divider />
      <span className={baseStyles.right_spanText}>时间同步</span>
      <List dataSource={timeConfigDataSource} /> */}

      <Modal title={'录制计划'} content={<RecordPlanPC propsCamera={curDevice!} callback={handleOnChange} />} isShow={recordPlayShow} onCancel={() => setRecordPlayShow(false)} footer={null}
        contentStyle={{ width: px2rem('500px'), height: px2rem('500px') }} />
    </div>
  )
}

export default DeviceRecordConfig;