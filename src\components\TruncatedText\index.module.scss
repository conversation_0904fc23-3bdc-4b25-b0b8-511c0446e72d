.textContainer {
  width: 100%;
  line-height: 1.5em;

  max-height: 3em; /* 默认两行 */
  overflow: hidden;
  white-space: normal;
  font-family: inherit;
  font-size: inherit;
  padding: 0;
  box-sizing: border-box;
  word-wrap: break-word;
  word-break: break-word;
  position: relative;
}

/* 当需要自定义截断时，覆盖默认的CSS省略样式 */
.textContainer.customTruncated {
  display: block !important;
  -webkit-box-orient: unset !important;
  -webkit-line-clamp: unset !important;
  text-overflow: unset !important;
}

.hidden {
  opacity: 0;
  pointer-events: none;
  position: absolute;
    color:var(--text-color);
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}