@media screen and (pointer: coarse) {
  @supports (-webkit-backdrop-filter: blur(1px)) and (overscroll-behavior-y: none) {
    html {
      min-height: 100%;
      overscroll-behavior-y: none;
    }
  }
}


@media screen and (pointer: coarse) {
  @supports (-webkit-backdrop-filter: blur(1px)) and (not (overscroll-behavior-y: none)) {
    html {
      height: 100%;
      overflow: hidden;
    }

    body {
      margin: 0px;
      max-height: 100%;
      overflow: auto;
      -webkit-overflow-scrolling: touch;
    }
  }
}

body {
  overscroll-behavior-y: none;
}


.monitorPlayer_container {
  background-color: var(--background-color);
  position: relative;
  transition: all 0.2s;

  /* .xg-inner-controls,
  .xgplayer-controls {
    display: none !important;
  }

  .xgplayer .gradient {
    display: none !important;
  } */

  user-select: none;
}

.monitorPlayer_top {
  width: 100%;
  display: flex;
  align-items: center;
  transition: opacity .5s ease, visibility .5s ease;
}

.monitorPlayer_top_devicePlugin {
  position: absolute;
  top: 30px;
}

.monitorPlayer_top_devicePlugin.full {
  top: 50px
}

.monitorPlayer_modal_top {
  display: flex;
  align-items: center;
  position: absolute;
  z-index: 1;
  width: 100%;
  padding: 28px;
  transition: opacity .5s ease, visibility .5s ease;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);

  img {
    width: 26px;
    height: 26px;
  }
}

.monitorPlayer_modal_top_title {
  font-family: MiSans;
  font-weight: 500;
  font-size: 20px;
  line-height: 100%;
  letter-spacing: 0px;
  color: #FFF;
  flex: 1;
  transform: translateX(50%);
}

.monitorPlayer_modal_top_spans {
  display: flex;
  align-items: center;

  img {
    width: 16px;
    height: 16px;
  }
}

.monitorPlayer_modal_top_span {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #FFF;
  font-family: MiSans;
  font-weight: 400;
  font-size: 10.1px;
  line-height: 100%;
  letter-spacing: 0%;
  text-align: center;
  margin-right: 14px;
  z-index: 10005;
}

.monitorPlayer_dual {
  position: absolute;
  top: 20px;
  right: 20px;
}

.monitorPlayer_top_close {
  position: absolute;
  z-index: 10005;
  left: 10px;
}

.monitorPlayer_top_close_img {
  height: 16px;
}

.monitorPlayer_hover_container {
  position: relative;
  user-select: none;
}

.monitorPlayer_hover_container_mask {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #000;
  opacity: 0.4;
}

.monitorPlayer_hover_container_play {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  cursor: pointer;
}

.monitorPlayer_hover_container_fail,
.monitorPlayer_hover_container_offline {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 50%;
  top: 50%;
  color: #fff;
  transform: translateX(-50%) translateY(-50%);

  img {
    height: 48px;
  }
}

.monitorPlayer_hover_container_fail_img {
  height: 48px;
}

.player_lookBackDetail_modal_container {
  width: calc(100% / 2);
}

.monitorPlayer_hover_content_poster {
  width: 100%;
  height: 100%;
}

.monitorPlayer_error_container {
  background-color: #252525;
  z-index: 999;
  position: absolute;
  top: 0;
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.monitorPlayer_error_container_closeBtn {
  cursor: pointer;
  position: absolute;
  top: 30px;
  left: 30px;

  img {
    width: 28px;
    height: 28px;
  }
}