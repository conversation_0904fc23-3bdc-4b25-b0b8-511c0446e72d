.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  background-color: var(--file-selector-bg);
  width: 100%;
}

.adm-popup-body{
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  
  .title {
    font-size: 20px;
    font-weight: 500;
    font-family: MiSans;
    color: var(--text-color);
  }
}

.breadcrumb {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background-color: var(--file-selector-bg);
  overflow-x: auto;
  white-space: nowrap;
  
  &::-webkit-scrollbar {
    display: none;
  }
  
  .breadcrumbItem {
    color: rgba(140, 147, 176, 1);
    background-color: rgba(140, 147, 176, 0.2);
    padding: 5px 10px;
    border-radius: 10px;
    font-size: 14px;
    cursor: pointer;
    
    &.active {
      background-color:rgba(255, 178, 29, 0.2);
      padding: 5px 10px;
      border-radius: 10px;
      color: rgba(255, 178, 29, 1);
      font-weight: 500;
    }
  }
  
  .breadcrumbSeparator {
    margin: 0 4px;
    color: var(--secondary-text-color, #8C93B0);
    font-size: 12px;
  }
}

// 加载状态样式
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 16px;
  color: var(--secondary-text-color, #8C93B0);
  
  span {
    margin-top: 12px;
    font-size: 14px;
  }
}

.fileList {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
  }
  
  // 空状态样式
  .emptyState {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60px 16px;
    color: var(--secondary-text-color, #8C93B0);
    font-size: 14px;
  }
  
  .fileItem {
    display: flex;
    align-items: center;
    padding: 12px 0;
    cursor: pointer;
    
    &:last-child {
      border-bottom: none;
    }
    
    .fileIcon {
      width: 40px;
      height: 40px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      
      
      
      .fileIconImage {
        width: 100%;
        height: 100%;
        background-color: #599CFA;
        border-radius: 4px;
      }
    }
    
    .fileInfo {
      flex: 1;
      overflow: hidden;
      
      .fileName {
        display: flex;
        align-items: center;
        font-size: 17px;
        font-family:MiSans W;
        font-weight: 500;
        color: var(--text-color);
        margin-bottom: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        
        .heartIcon {
          margin-left: 8px;
          color: #FF5C5C;
          font-size: 14px;
        }
      }
      
      .fileTime {
        font-size: 12px;
        color: var(--list-value-text-color);
        margin-bottom: 2px;
                font-family:MiSans W;
        font-weight: 500;
      }
      
      .itemCount {
        font-size: 12px;
        color: var(--secondary-text-color, #8C93B0);
      }
      
      .fileDetails {
        font-size: 12px;
        color: var(--secondary-text-color, #8C93B0);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    
    .rightArrow {
      color: var(--secondary-text-color, #8C93B0);
      margin-left: 8px;
    }
  }
}

.footer {
  padding: 16px;
  background-color: var(--file-selector-bg);
  display: flex;
  gap: 12px;
  
  .createButton {
    flex: 1;
    height: 50px;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 500;
    background-color: #F5F5F5;
    color: #666666;
    border: none;
    
    &:active {
      background-color: #E5E5E5;
    }
  }
  
  .selectButton {
    flex: 1;
    height: 58px;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 500;
    font-family: MiSans;
    &:disabled {
      background-color: #E5E5E5;
      color: #B3B3B3;
      border: none;
    }
  }
}
