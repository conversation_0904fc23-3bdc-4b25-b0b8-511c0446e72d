// 网盘NAS会员页面样式
.minePage {
//   min-height: 100vh;
  background-color: var(--background-color);
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

// 用户信息区域
.userInfoSection {
  background-color: var(--background-color);
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.userProfile {
  display: flex;
  align-items: center;
  gap: 20px;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
}

.userDetails {
  flex: 1;
}

.userName {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 8px 0;
}

.accountInfo,
.storageInfo {
  font-size: 14px;
  color: var(--text-color);
  margin: 4px 0;
}

.unbindBtn {
  color: var(--text-color);
  width: 107px;
  text-align: center;
  height: 40px;
  font-weight: 500;
  padding: 10px 10px;
  font-size: 14px;
  border-radius: 10px;
  background-color: var(--componentcard-btn-bg-color);
}

// 会员状态区域
.membershipSection {
background-color: var(--background-color);;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.membershipStatus {
  display: flex;
  align-items: center;
  gap: 40px;
  flex-wrap: wrap;
}

.statusItem {
  display: flex;
  align-items: center;
  gap: 8px;
}

.statusIcon {
  font-size: 16px;
  opacity: 0.6;
}

.statusText {
  font-size: 14px;
  color: var(--text-color);
}

// 会员状态颜色
.svipActive {
  color: #3D98FF !important;
}

.nasVipActive {
  color: #BF751F !important;
}

.memberInactive {
  color: #7D7D7D !important;
}

.upgradeBtn {
  background-color: #402C00;
  border-color: #8B4513;
  color:rgba(226, 174, 30, 1);
  border-radius: 20px;
  height: 45px;
  width:280px;
  padding: 0 32px;
  font-size: 14px;
  font-weight: 500;
  margin-left: auto;
  
  &:hover {
    background-color: #A0522D;
    border-color: #A0522D;
  }
  
  &:active {
    background-color: #654321;
    border-color: #654321;
  }
}

// 特权对比区域
.privilegeSection {
  background-color: var(--background-color);
  border:1px solid #ccc;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.privilegeTitle {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0 0 24px 0;
  text-align: center;
}

.privilegeTable {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e6e6e6;
}

.tableHeader {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  background-color: #f8f9fa;
}

.headerCell {
  padding: 16px;
  text-align: center;
  font-weight: 600;
  color: var(--text-color);
  border-right: 1px solid #e6e6e6;
  background-color: var(--search-tab-bg);
  &:last-child {
    border-right: none;
  }
}

.nasVipHeader {
  background-color: #402C00;
  color: rgba(226, 174, 30, 1);
}

.tableRow {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  border-bottom: 1px solid #e6e6e6;
  
  &:last-child {
    border-bottom: none;
  }
}

.cell {
  padding: 16px;
  text-align: center;
  font-size: 14px;

  border-right: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:last-child {
      background-color: var(--componentcard-btn-bg-color);
      color: var(--text-color);
    border-right: none;
  }
  
  &:first-child {
      background-color: var(--componentcard-btn-bg-color);
      color: var(--text-color);
    font-weight: 500;
  }
}

.nasVipCell {
  background-color: #724E00;
  color: rgba(226, 174, 30, 1);
  font-weight: 500;
}
.sVipCell{
    background-color: #FFFBEF;
    color:rgba(90, 59, 31, 1);
}
.sVipHeader{
    background-color: #FFF5D8;
    color: rgba(92, 46, 20, 1);
    font-weight: 500;
}

.checkIcon {
  color: rgba(226, 174, 30, 1);
  font-size: 16px;
  font-weight: bold;
}

.dashIcon {
  color: #999;
  font-size: 14px;
}

// 底部客服信息
.footer {
  text-align: center;
  padding: 20px 0;
}

.serviceInfo {
  font-size: 12px;
  color: #999;
  margin: 0;
}

