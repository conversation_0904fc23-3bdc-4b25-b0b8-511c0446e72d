import React, { useCallback, useState, useEffect } from "react";
import {
  Ellipsis,
  Image,
  Input,
  List,
  Modal,
  Popover,
  Toast,
} from "antd-mobile";
import { useHistory, useLocation } from "react-router-dom";
import classNames from "classnames";
import styles from "./index.module.scss";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import addPhoto from "@/Resources/camMgmtImg/add-photo.png";
import close from "@/Resources/camMgmtImg/close.png";
import finish from "@/Resources/camMgmtImg/finish.png";
import selected from "@/Resources/camMgmtImg/selected.png";
import unchecked from "@/Resources/camMgmtImg/unchecked.png";
// import edit from "@/Resources/camMgmtImg/edit.png";
import deletes from "@/Resources/camMgmtImg/delete.png";
import more from "@/Resources/camMgmtImg/more.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import closeDark from "@/Resources/camMgmtImg/close-dark.png";
import moreDark from "@/Resources/camMgmtImg/more-dark.png";
// import editDark from "@/Resources/camMgmtImg/edit-dark.png";
import deletesDark from "@/Resources/camMgmtImg/deletes-dark.png";
import finishDark from "@/Resources/camMgmtImg/finish-dark.png";
import { modalShow } from "@/components/List";
import { useRequest } from "ahooks";
import {
  setFacialInfo,
  FacialInfo,
  getFacialPhoto,
  delFacialPhoto,
  delFacialVideo,
  getFacialVideo,
  getFacialInfo,
} from "@/api/ipc";
import { setIPCUserIcon } from "@/api/cameraPlayer";

import {
  PhotoItem,
  VideoItem,
  togglePhotoSelection as togglePhoto,
  getPhotoSrc,
  getVideoDesc,
} from "./faceRecognition";
import { useTheme } from "@/utils/themeDetector";
import { IlLookBackData } from "@/components/CameraPlayer/components/plugin/ControlPlugin/PlayerControl";
import { PreloadImage } from "@/components/Image";
import { splitURL } from "@/components/CameraPlayer/components/MonitorPlayer/MonitorPlayer";
interface LocationState {
  face: FacialInfo;
}

const FaceDetailPage = () => {
  const history = useHistory();
  const location = useLocation<LocationState>();
  const { isDarkMode } = useTheme();

  const { face } = location.state || {};
  const [activeTab, setActiveTab] = useState<string>("photos");
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);
  const [popoverVisible, setPopoverVisible] = useState<boolean>(false);
  const [remark, setRemark] = useState<string>(face?.name);
  const [tempRemark, setTempRemark] = useState<string>(face?.name);
  const [, setIsInputFocused] = useState<boolean>(false);
  const [selectedAvatar, setSelectedAvatar] = useState<string>(
    face?.profile_pic
  );
  const [selectedPhotos, setSelectedPhotos] = useState<string[]>([]);
  const [, setIsEditingPhotos] = useState<boolean>(false);
  const isUnrecorded = face?.name === "未标记";

  // 存储编辑前的照片列表备份（用于取消编辑时恢复）
  const [originalPhotoList, 
    // setOriginalPhotoList
  ] = useState<PhotoItem[]>([]);
  // 存储临时删除的照片列表（只有点击右上角完成才真正删除）
  const [temporaryDeletedPhotos, setTemporaryDeletedPhotos] = useState<
    PhotoItem[]
  >([]);

  // 获取人脸信息（用于更新头像）
  const { run: refreshFaceInfo } = useRequest(
    () => getFacialInfo(),
    {
      manual: true,
      onSuccess: (res) => {
        if (res && res.code === 0) {
          // 找到当前人脸的最新信息
          const updatedFace = res.data.info.find(f => f.uuid === face.uuid);
          if (updatedFace) {
            // 更新头像
            setSelectedAvatar(updatedFace.profile_pic);
          }
        }
      },
      onError: (error) => {
        console.error("获取人脸信息失败:", error);
      },
    }
  );

  // 获取人脸照片
  const { run: refreshPhotos } = useRequest(
    () => getFacialPhoto({ uuid: face.uuid }),
    {
      onSuccess: (res) => {
        if (res && res.code === 0) {
          // 直接使用返回的照片路径数组
          const photos = res.data.photo.map((url, index) => ({
            id: `photo-${index}`,
            url: url || '',
          }));
          setPhotoList([{ id: "add", type: "add", url: addPhoto }, ...photos]);
          // 更新头像选项
          setAvatarOptions(photos);
        } else {
          Toast.show({
            content: res?.result,
          });
        }
      },
      onError: (error) => {
        console.error("获取人脸照片失败:", error);
      },
    }
  );

  // 获取视频列表
  const { run: refreshVideos } = useRequest(
    () =>
      getFacialVideo({
        page: {
          size: 20,
          token: "",
        },
        uuid: face.uuid,
      }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res && res.code === 0) {
          // 处理视频数据，添加日期和是否今天的标记
          const videos = res.data.videos.map((video: any, index: number) => {
            const date = new Date(parseInt(video.time));
            const today = new Date();
            const isToday = date.toDateString() === today.toDateString();

            return {
              ...video,
              id: `video-${index}`,
              camera_lens: video.camera_lens || "",
              event_name: video.event_name || "face",
              face_file: video.face_file,
              cover_file: video.cover_file,
              date: date.toLocaleDateString(),
              isToday,
              subText: `识别到${face.name}`,
            };
          });

          setVideoData(videos);
        } else {
          Toast.show({
            content: res?.result,
          });
        }
      },
      onError: (error) => {
        console.error("获取视频列表失败:", error);
        Toast.show({
          content: "获取视频列表失败，请重试",
        });
      },
    }
  );

  // 在组件初始化时加载照片和视频数据
  useEffect(() => {
    // 照片数据在默认加载时已经加载（非manual）
    // 加载视频数据
    refreshVideos();
  }, [refreshVideos]);

  // 照片列表数据 - 使用与 API 相同的数据结构
  const [photoList, setPhotoList] = useState<PhotoItem[]>([]);

  // 头像选项数据
  const [, setAvatarOptions] = useState<PhotoItem[]>([]);

  const [videoData, setVideoData] = useState<VideoItem[]>([]);

  // 设置人脸信息
  const { run: saveFaceInfo } = useRequest(setFacialInfo, {
    manual: true,
    onSuccess: (res) => {
      if (res && res.code === 0) {
        Toast.show({
          content: "保存成功",
          position: "bottom",
          duration: 1000,
        });
        setIsEditingPhotos(false);
        // 更新头像后刷新照片列表
        refreshPhotos();
      } else {
        Toast.show({
          content: res?.result,
        });
      }
    },
    onError: (error) => {
      console.error("保存人脸信息失败:", error);
      Toast.show({
        content: "保存失败，请重试",
      });
    },
  });

  // 删除人脸照片
  const { run: deletePhotos } = useRequest(delFacialPhoto, {
    manual: true,
    onSuccess: (res) => {
      if (res && res.code === 0) {
        Toast.show({
          content: "删除成功",
          position: "bottom",
          duration: 1000,
        });
        // 刷新照片列表
        refreshPhotos();
      } else {
        Toast.show({
          content: res?.result,
        });
      }
    },
    onError: (error) => {
      console.error("删除照片失败:", error);
      Toast.show({
        content: "删除失败，请重试",
      });
    },
  });

  // 删除视频
  const { run: deleteVideo } = useRequest(delFacialVideo, {
    manual: true,
    onSuccess: (res) => {
      if (res && res.code === 0) {
        Toast.show({
          content: "删除成功",
          position: "bottom",
          duration: 1000,
        });
        // 重新获取视频列表
        refreshVideos();
      } else {
        Toast.show({
          content: res?.result,
        });
      }
    },
    onError: (error) => {
      console.error("删除视频失败:", error);
      Toast.show({
        content: "删除失败，请重试",
      });
    },
  });

  // 删除视频处理函数
  const handleDeleteVideo = (videoId: string) => {
    const video = videoData.find((v) => v.id === videoId);
    if (!video) return;

    // 使用useRequest处理删除操作
    deleteVideo({
      uuid: face.uuid,
      video: [video.file],
    });
  };

  // 保存备注
  const handleSaveRemark = () => {
    if (!tempRemark.trim()) {
      Toast.show({
        content: "请输入备注名称",
        position: "center",
      });
      return;
    }

    // 调用API保存备注
    saveFaceInfo({
      uuid: face.uuid,
      name: tempRemark.trim(),
      profile_pic: face.profile_pic,
      delete: false,
    });

    // 更新显示的备注名并关闭弹窗
    setRemark(tempRemark.trim());
    setIsModalVisible(false);
  };

  // 处理打开相册选择头像
  const handleOpenAlbumForAvatar = () => {
    try {
      // 调用APP方法打开相册选择头像
      setIPCUserIcon(face.uuid,(response) => {
        if (response && response.code === 0) {
          Toast.show({
            content: "头像设置成功",
            position: "bottom",
            duration: 1000,
          });
          // 刷新人脸信息以更新头像
          refreshFaceInfo();
          // 刷新照片列表
          refreshPhotos();
        } else {
          Toast.show({
            content: response?.msg || "头像设置失败",
            position: "bottom",
            duration: 1000,
          });
        }
      }).catch(error => {
        console.error("设置头像失败:", error);
        Toast.show({
          content: "头像设置失败，请重试",
          position: "bottom",
          duration: 1000,
        });
      });
    } catch (error) {
      console.error("调用APP方法失败:", error);
      Toast.show({
        content: "无法打开相册，请重试",
        position: "bottom",
        duration: 1000,
      });
    }
  };

  // 视频回放
  const lookBackDetail = useCallback(
    (video) => {
      const lookBackData: IlLookBackData = {
        type: "movie",
        url: video?.file,
      };
      history.push({
        pathname: "/cameraManagement_app/cameraDetail/lookBackDetail",
        state: {
          lookBackData: lookBackData,
          face: face, // 添加face数据，以便返回时能够获取
          fromPage: "faceDetail" // 添加来源标记
        },
      });
    },
    [history, face]
  );

  // 进入照片编辑模式
  // const enterPhotoEditMode = () => {
  //   // 在进入编辑模式时保存当前照片列表，以便取消编辑时恢复
  //   setOriginalPhotoList([...photoList]);
  //   setIsEditingPhotos(true);
  //   setSelectedPhotos([]);
  //   setTemporaryDeletedPhotos([]);
  // };

  // 退出照片编辑模式
  const exitPhotoEditMode = () => {
    setIsEditingPhotos(false);
    setSelectedPhotos([]);
  };

  // 处理照片选择
  const handlePhotoSelection = (photoId: string) => {
    togglePhoto(
      photoId,
      photoList.filter((p) => p.type !== "add"),
      (photos) => {
        const addPhoto = photoList.find((p) => p.type === "add");
        setPhotoList([...(addPhoto ? [addPhoto] : []), ...photos]);
      },
      selectedPhotos,
      (ids: (string | number)[]) => setSelectedPhotos(ids as string[])
    );
  };

  // 临时删除选中照片 - 只在当前 modal 中移除，实际上软删除
  const handleTemporaryDeleteSelectedPhotos = () => {
    if (selectedPhotos.length === 0) return;

    // 将被选中的照片添加到临时删除列表
    const photosToDelete = photoList.filter(
      (photo) =>
        photo.type !== "add" && selectedPhotos.includes(photo.id as string)
    );
    setTemporaryDeletedPhotos([...temporaryDeletedPhotos, ...photosToDelete]);

    // 过滤掉被选中的照片
    const updatedPhotos = photoList.filter(
      (photo) =>
        photo.type === "add" || !selectedPhotos.includes(photo.id as string)
    );

    setPhotoList(updatedPhotos);
    setSelectedPhotos([]);
  };

  // 取消编辑 - 恢复原始照片列表
  const cancelPhotoEdit = () => {
    setPhotoList([...originalPhotoList]);
    setTemporaryDeletedPhotos([]);
    setEditModalVisible(false);
    exitPhotoEditMode();
  };

  // 确认编辑 - 永久删除临时删除的照片
  const confirmPhotoEdit = () => {
    if (temporaryDeletedPhotos.length > 0) {
      // 获取要删除的照片URL列表
      const photosToDelete = temporaryDeletedPhotos
        .map((photo) => photo.url)
        .filter((url): url is string => url !== undefined);

      // 调用删除API
      deletePhotos({
        uuid: face.uuid,
        photo: photosToDelete,
      });
    }

    setEditModalVisible(false);
    exitPhotoEditMode();
    // 刷新照片列表
    refreshPhotos();
  };

  // 渲染照片内容
  // const renderPhotoContent = () => {
  //   return (
  //     <div className={styles.photoGrid}>
  //       {photoList.map((item) => (
  //         <div key={item.id} className={styles.photoItem}>
  //           {item.type === "add" ? (
  //             <div>
  //               <Image
  //                 src={getPhotoSrc(item)}
  //                 onClick={() => Toast.show("一期暂不支持")}
  //               />
  //             </div>
  //           ) : (
  //             <PreloadImage
  //               src={splitURL(getPhotoSrc(item))}
  //               className={styles.photo}
  //               needHeader={true}
  //             />
  //           )}
  //         </div>
  //       ))}
  //     </div>
  //   );
  // };

  // 渲染视频内容
  const renderVideoContent = () => (
    <div className={styles.videoList}>
      {videoData.map((video, index) => {
        return (
          <React.Fragment key={video.id}>
            {(index === 0 || video.date !== videoData[index - 1]?.date) && (
              <div className={styles.dateHeader}>
                <div className={styles.date}>{video.date}</div>
                <div className={styles.subDate}>
                  {video.isToday ? "今天" : ""}
                </div>
              </div>
            )}
            <div className={styles.videoItem}>
              <div className={styles.videoInfo}>
                <PreloadImage
                  src={splitURL(video.face_file || "")}
                  className={styles.videoAvatar}
                  needHeader={true}
                  style={{width:32,height:32}}
                />
                <div className={styles.videoTimeInfo}>
                  <div className={styles.videoTime}>
                    {new Date(parseInt(video.time)).toLocaleTimeString()}
                  </div>
                  <div className={styles.videoSubText}>
                    <Ellipsis direction="end" content={getVideoDesc(video)} />
                  </div>
                </div>
              </div>
              <div className={styles.videoPreviewContainer}>
                <div className={styles.videoPreview}>
                  <PreloadImage
                    src={splitURL(video.cover_file || "")}
                    className={styles.videoThumbnail}
                    needHeader={true}
                    onClick={() => lookBackDetail(video)}
                  />
                </div>
                <div
                  className={styles.deleteButton}
                  onClick={() => handleDeleteVideo(video.id as string)}
                >
                  报错
                </div>
              </div>
            </div>
          </React.Fragment>
        );
      })}
    </div>
  );

  // 照片编辑模态框
  const PhotoEditModal = () => {
    if (!editModalVisible) return null;

    return (
      <div className={styles.photoEditModal}>
        <div className={styles.photoEditHeader}>
          <div className={styles.photoEditLeft} onClick={cancelPhotoEdit}>
            <Image
              src={isDarkMode ? closeDark : close}
              className={styles.photoEditBackIcon}
            />
          </div>
          <div className={styles.photoEditTitle}>编辑</div>
          <div className={styles.photoEditRight} onClick={confirmPhotoEdit}>
            <Image
              src={isDarkMode ? finishDark : finish}
              className={styles.photoEditCheckIcon}
            />
          </div>
        </div>
        <div className={styles.photoEditContent}>
          <div className={styles.photoEditGrid}>
            {photoList
              .filter((photo) => photo.type !== "add")
              .map((item) => (
                <div
                  key={item.id}
                  className={styles.photoEditItem}
                  onClick={() => handlePhotoSelection(item.id as string)}
                >
                  <div className={styles.photoEditItemWrapper}>
                    <PreloadImage
                      src={splitURL(getPhotoSrc(item))}
                      className={styles.photoEditImage}
                      needHeader={true}
                    />
                    {selectedPhotos.includes(item.id as string) ? (
                      <Image
                        className={classNames(styles.photoEditSelectCircle, {
                          [styles.photoEditSelected]: selectedPhotos.includes(
                            item.id as string
                          ),
                        })}
                        src={selected}
                      ></Image>
                    ) : (
                      <Image
                        className={classNames(styles.photoEditSelectCircle, {
                          [styles.photoEditSelected]: selectedPhotos.includes(
                            item.id as string
                          ),
                        })}
                        src={unchecked}
                      ></Image>
                    )}
                  </div>
                </div>
              ))}
          </div>
        </div>

        {/* 底部删除按钮 */}
        {selectedPhotos.length > 0 && (
          <div className={styles.photoEditDeleteBar}>
            <div
              className={styles.photoEditDeleteButton}
              onClick={handleTemporaryDeleteSelectedPhotos}
            >
              <Image
                src={isDarkMode ? deletesDark : deletes}
                className={styles.photoEditDeleteIcon}
              />
              <span>删除</span>
            </div>
          </div>
        )}
      </div>
    );
  };

  // 处理删除用户
  const handleDeleteUser = () => {
    setPopoverVisible(false);
    modalShow(
      "",
      <div className={styles.deleteConfirmText}>删除后，该任务将以陌生人显示录像保留，确认删除？</div>,
      (m) => {
        m.destroy();
        saveFaceInfo({
          uuid: face.uuid,
          profile_pic: selectedAvatar || '',
          name: '',
          delete: false,
        });
        Toast.show({
          content: "删除成功",
          position: "bottom",
        });
        history.goBack();
      },
      () => {}, // onCancel
      false, // onlyShow
      {
        okBtnText: "删除",
        cancelBtnText: "取消",
        okBtnStyle: {
          color: "var(--emergency-text-color)",
          background: "var(--cancel-btn-background-color)",
        },
        position: "bottom",
      }
    );
  };

  // 处理彻底删除
  const handlePermanentDelete = () => {
    setPopoverVisible(false);
    modalShow(
      "",
      <div className={styles.deleteConfirmText}>将删除该人物所有识别信息及相关视频，确认删除？</div>,
      (m) => {
        m.destroy();
        saveFaceInfo({
          uuid: face.uuid,
          profile_pic: selectedAvatar || '',
          name: remark || '',
          delete: true,
        });
        Toast.show({
          content: "彻底删除成功",
          position: "bottom",
        });
        history.goBack();
      },
      () => {}, // onCancel
      false, // onlyShow
      {
        okBtnText: "删除",
        cancelBtnText: "取消",
        okBtnStyle: {
          color: "var(--emergency-text-color)",
          background: "var(--cancel-btn-background-color)",
        },
        position: "bottom",
      }
    );
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Image
          className={styles.close}
          src={isDarkMode ? arrowLeftDark : arrowLeft}
          onClick={() => history.goBack()}
          style={{width:40,height:40}}
        />
        <Popover
          visible={popoverVisible}
          onVisibleChange={setPopoverVisible}
          content={
            <div className={styles.popoverContent}>
              <div className={styles.popoverItem} onClick={handleDeleteUser}>
                <span className={styles.deleteText}>删除标记</span>
              </div>
              <div className={styles.popoverDivider}></div>
              <div className={styles.popoverItem} onClick={handlePermanentDelete}>
                <span className={styles.deleteText}>彻底删除</span>
              </div>
            </div>
          }
          trigger="click"
          placement="bottom-end"
        >
          <Image
            className={styles.outline}
            src={isDarkMode ? moreDark : more}
            style={{width:40,height:40}}
          />
        </Popover>
      </div>
      <div className={styles.title}>人脸管理</div>

      {/* 个人信息部分 */}
      <div className={styles.profileWrapper}>
        <List className={styles.profileList}>
          <List.Item
            prefix={
              <div
                className={styles.avatarWrapper}
                onClick={handleOpenAlbumForAvatar}
              >
                <PreloadImage
                  src={splitURL(selectedAvatar || "")}
                  className={styles.avatar}
                  needHeader={true}
                />
                <div className={styles.avatarText}>修改头像</div>
              </div>
            }
            title={<Ellipsis direction="end" rows={3} content={remark} />}
            extra={
              <div
                className={styles.extraContent}
                onClick={() => {
                  setTempRemark(remark);
                  setIsModalVisible(true);
                }}
              >
                <span
                  className={isUnrecorded ? styles.markText : styles.editText}
                >
                  {isUnrecorded ? "请标记" : "修改备注"}
                </span>
              </div>
            }
            arrow={true}
            onClick={() => {}}
          />
        </List>
      </div>

      <div className={classNames(styles.thinLine, styles.customMargin)} />

      <div className={styles.content}>
        <div className={styles.tabsHeader}>
          {/* <div
            className={classNames(styles.tabItem, {
              [styles.activeTab]: activeTab === "photos",
            })}
            onClick={() => setActiveTab("photos")}
          >
            照片
          </div> */}
          <div
            className={classNames(styles.tabItem, {
              [styles.activeTab]: activeTab === "videos",
            })}
            onClick={() => setActiveTab("videos")}
          >
            最近视频
          </div>
          {/* {activeTab === "photos" && (
            <Image
              className={styles.editIcon}
              src={isDarkMode ? editDark : edit}
              onClick={() => {
                setEditModalVisible(true);
                enterPhotoEditMode();
              }}
            />
          )} */}
        </div>

        {/* {activeTab === "photos" ? renderPhotoContent() : renderVideoContent()} */}
        {renderVideoContent()}
      </div>

      {/* 修改备注弹窗 */}
      <Modal
        className={styles.modalBox}
        visible={isModalVisible}
        content={
          <div className={styles.modalContent}>
            <div className={styles.modalHeader}>
              {isUnrecorded ? "标记" : "备注名"}
            </div>
            <Input
              className={styles.remarkInput}
              value={tempRemark}
              onChange={setTempRemark}
              placeholder={isUnrecorded ? "请输入标记名称" : "请输入备注"}
              onFocus={() => setIsInputFocused(true)}
              onBlur={() => setIsInputFocused(false)}
              autoFocus
              clearable
            />
            <div className={styles.modalFooter}>
              <button
                className={styles.cancelButton}
                onClick={() => setIsModalVisible(false)}
              >
                取消
              </button>
              <button
                className={styles.confirmButton}
                onClick={handleSaveRemark}
              >
                确定
              </button>
            </div>
          </div>
        }
        closeOnAction
        onClose={() => setIsModalVisible(false)}
      />

      {/* 显示照片编辑模态框 */}
      <PhotoEditModal />
    </div>
  );
};

export default FaceDetailPage;
