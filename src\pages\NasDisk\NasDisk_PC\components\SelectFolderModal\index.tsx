import React, { useState, useEffect } from 'react';
import { Modal, Button, Checkbox, List, message } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import styles from './index.module.scss';
import { PreloadImage } from '@/components/Image';
import fileIcon from '@/Resources/nasDiskImg/file-icon.png';
import EmptyState from '../EmptyState';
import { getBaiduNetdiskFileList, BaiduFileItem } from '@/api/nasDisk';
import DownloadModal from '../DownloadModal';
import { modalShow } from '@/components/List';
import { useHistory } from 'react-router-dom';
import { useUser } from '@/utils/UserContext';

interface FileItem {
  id: string;
  name: string;
  type: 'folder' | 'file';
  time: string;
  isLiked?: boolean;
  path: string;
  disabled?: boolean; // 添加disabled属性表示该文件夹是否已在下载
}

interface SelectFolderModalProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (folders: FileItem[], selectedPath?: string, selectedDisplayPath?: string) => void;
  maxTaskCount?: number;
  downloadPath?: string; // 默认下载路径
  downloadDisplayPath?: string; // 默认下载路径显示文本
  existingFolderPaths?: string[]; // 添加已有任务的文件夹路径
}

const SelectFolderModal: React.FC<SelectFolderModalProps> = ({
  visible,
  onClose,
  onSelect,
  maxTaskCount = 5,
  downloadPath: initialDownloadPath = "",
  downloadDisplayPath: initialDownloadDisplayPath = "内部存储01/百度网盘",
  existingFolderPaths = []
}) => {
   const history = useHistory();
    const { userInfo } = useUser();
    const { nas_vip } = userInfo || {};
  // 文件夹列表
  const [folderList, setFolderList] = useState<FileItem[]>([]);
  // 选中的文件夹ID列表
  const [selectedFolderIds, setSelectedFolderIds] = useState<string[]>([]);
  // 下载位置显示文本
  const [downloadLocation, setDownloadLocation] = useState<string>(initialDownloadDisplayPath);
  // 下载位置实际路径
  const [downloadPath, setDownloadPath] = useState<string>(initialDownloadPath);
  // 是否显示下载位置选择弹窗
  const [showDownloadModal, setShowDownloadModal] = useState<boolean>(false);
  // 模态框key，用于强制刷新
  const [modalKey, setModalKey] = useState<number>(0);

  // 标准化路径，确保以/开头且不以/结尾
  const normalizePath = (path: string): string => {
    let normalizedPath = path;
    // 确保路径以/开头
    if (!normalizedPath.startsWith('/')) {
      normalizedPath = '/' + normalizedPath;
    }
    // 确保路径不以/结尾（除非就是根路径/）
    if (normalizedPath !== '/' && normalizedPath.endsWith('/')) {
      normalizedPath = normalizedPath.slice(0, -1);
    }
    return normalizedPath;
  };

  // 获取百度网盘文件列表
  const { run: fetchFileList } = useRequest(
    () => {
      // 调用真实接口
      return getBaiduNetdiskFileList({
        action: "remotelist",
        path: "/", // 固定只获取根目录
        order: "name",
        desc: 0,
        web: 1,
        folder: 1, // 只返回文件夹
      }).catch(error => {
        console.error('获取文件列表失败，使用模拟数据', error);
      });
    },
    {
      manual: true,
      onSuccess: (response) => {
        if (response && response.errno === 0) {
          // 将百度网盘文件列表转换为应用内文件列表格式
          const folders: FileItem[] = response.list
            .filter((item: BaiduFileItem) => item.isdir === 1) // 只保留文件夹
            .map((item: BaiduFileItem) => {
              const folderPath = normalizePath(item.path);
              
              // 检查此文件夹路径是否已存在于下载任务中
              const isDisabled = existingFolderPaths.some(taskPath => {
                const normalizedTaskPath = normalizePath(taskPath);
                return normalizedTaskPath === folderPath;
              });
              
              return {
                id: item.fs_id.toString(),
                name: item.server_filename,
                type: "folder",
                time: new Date(item.server_mtime * 1000).toLocaleString('zh-CN', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit',
                }),
                path: item.path,
                isLiked: false,
                disabled: isDisabled // 根据是否已存在于下载任务中来设置禁用状态
              };
            });

          setFolderList(folders);
        } else {
          console.error("获取文件列表失败:", response);
          message.error("获取文件列表失败，请重试");
          setFolderList([]);
        }
      },
      onError: (error) => {
        console.error("获取文件列表失败：", error);
        message.error("获取文件列表失败，请重试");
        setFolderList([]);
      },
    }
  );

  // 弹窗显示时获取文件列表和初始化下载路径
  useEffect(() => {
    if (visible) {
      // 每次打开弹窗时增加key，强制刷新组件
      setModalKey(prevKey => prevKey + 1);
      fetchFileList();
      setSelectedFolderIds([]);
      
      // 如果有传入的下载路径，更新状态
      if (initialDownloadPath) {
        setDownloadPath(initialDownloadPath);
      }
      if (initialDownloadDisplayPath) {
        setDownloadLocation(initialDownloadDisplayPath);
      }
    }
  }, [visible, fetchFileList, initialDownloadPath, initialDownloadDisplayPath]);

  // 处理文件夹选择
  const handleFolderSelect = (folder: FileItem) => {
    // 如果文件夹已禁用（已在下载中），则不允许选择
    if (folder.disabled) {
      message.warning("该文件夹已在下载任务中");
      return;
    }

    setSelectedFolderIds(prev => {
      const isSelected = prev.includes(folder.id);
      
      if (isSelected) {
        // 如果已选中，则移除
        return prev.filter(id => id !== folder.id);
      } else {
        // 如果未选中，检查是否超过限制
        if (prev.length >= maxTaskCount) {
          message.warning(`最多只能选择${maxTaskCount}个文件夹`);
          return prev;
        }
        // 添加到选中列表
        return [...prev, folder.id];
      }
    });
  };

  // 处理Checkbox点击事件
  const handleCheckboxClick = (e: React.MouseEvent, folder: FileItem) => {
    // 阻止事件冒泡，避免触发List.Item的onClick
    e.stopPropagation();
    
    // 如果文件夹已禁用，则不允许选择
    if (folder.disabled) {
      message.warning("该文件夹已在下载任务中");
      return;
    }
    
    handleFolderSelect(folder);
  };

  // 处理确认按钮点击
  const handleConfirm = () => {
    if (selectedFolderIds.length === 0) {
      message.error("请至少选择一个文件夹");
      return;
    }
    
    // 根据选中的ID获取对应的文件夹对象
    const selectedFolders = folderList.filter(folder => 
      selectedFolderIds.includes(folder.id)
    );
    
    onSelect(selectedFolders, downloadPath, downloadLocation);
  };

  // 打开下载位置选择弹窗
  const handleSelectDownloadLocation = () => {
    // PC端自动下载位置选择埋点
    window.onetrack?.('track', 'nasDisk_autoDownload_location_click', {
      downloadPath: downloadLocation
    });

    if (nas_vip !== 1) {
      // 百度网盘VIP弹窗显示埋点
      window.onetrack?.('track', 'nasDisk_baiduNetdisk_vip_modal_show');

          modalShow(
            "会员权益",
            "该功能为网盘NAS会员权益，是否要开启？",
            (m) => {
              // 确认按钮点击 - 开通百度会员埋点
              window.onetrack?.('track', 'nasDisk_baiduNetdisk_vip_open_click');
              m.destroy();
              history.push(`/baiduNetdisk_pc/mine`);
            },
            () => {
              // 取消按钮点击
            },
            false,
            {
              position: "center",
              okBtnText: "开通会员",
              cancelBtnText: "取消",
              okBtnStyle: { backgroundColor: "#402C00", color: "#E2AE1E" },
            }
          );
          return;
        }
    setShowDownloadModal(true);
  };

  // 处理下载位置选择
  const handleDownloadLocationSelected = (path: string, displayPath: string) => {
    setDownloadPath(path);
    setDownloadLocation(displayPath);
    setShowDownloadModal(false);
  };

  return (
    <>
      <Modal
        key={`folder-select-modal-${modalKey}`}
        title={
          <div className={styles.modalHeader}>
            <ArrowLeftOutlined className={styles.backIcon} onClick={onClose} />
            <span className={styles.modalTitle}>选择文件夹（最多{maxTaskCount}项）</span>
          </div>
        }
        open={visible}
        footer={null}
        onCancel={onClose}
        width={546}
        centered
        closable={false}
        className={styles.folderSelectModal}
        styles={{
          body: {
            height: "calc(636px - 90px)",
            padding: 0,
            overflow: "hidden",
          },
        }}
      >
        <div className={styles.modalContent}>
          <div className={styles.folderSelectHeader}>
            <div className={styles.folderTag}>百度网盘</div>
          </div>
          
          <div className={styles.fileListContainer}>
            {folderList.length === 0 ? (
              <div className={styles.emptyContainer}>
                <EmptyState 
                />
              </div>
            ) : (
              <List
                className={styles.fileList}
                dataSource={folderList}
                renderItem={(folder) => (
                  <List.Item
                    key={folder.id}
                    className={`${styles.fileItem} ${selectedFolderIds.includes(folder.id) ? styles.selectedItem : ''} ${folder.disabled ? styles.disabledItem : ''}`}
                    onClick={() => !folder.disabled && handleFolderSelect(folder)}
                  >
                    <div className={`${styles.fileContent} ${selectedFolderIds.includes(folder.id) ? styles.selectedContent : ''}`}>
                      <PreloadImage
                        src={fileIcon}
                        alt="folder"
                        className={styles.fileIcon}
                      />
                      <div className={styles.fileInfo}>
                        <div className={styles.fileName}>
                          {folder.name}
                        </div>
                        <div className={styles.fileDetails}>
                          {folder.time}
                        </div>
                      </div>
                    </div>
                    <div
                      className={styles.checkboxCell}
                      onClick={(e) => handleCheckboxClick(e, folder)}
                    >
                      <Checkbox
                        checked={selectedFolderIds.includes(folder.id)}
                        disabled={folder.disabled}
                        className={styles.checkbox}
                      />
                    </div>
                  </List.Item>
                )}
              />
            )}
          </div>
          
          <div className={styles.footerContainer}>
            <div className={styles.uploadPathContainer}>
              {nas_vip !== 1 && (<div className={styles.vipTip}>VIP专享</div>)}
              <Button 
                className={styles.uploadPathButton}
                onClick={handleSelectDownloadLocation}
              >
                <span className={styles.pathLabel}>自动下载到: </span>
                <span className={styles.pathContainer}>
                  <span className={styles.uploadPath}>{downloadLocation}</span>
                </span>
              </Button>
            </div>
            <Button 
              type="primary" 
              className={styles.uploadButton}
              disabled={selectedFolderIds.length === 0}
              onClick={handleConfirm}
            >
              确定 {selectedFolderIds.length > 0 && `(${selectedFolderIds.length})`}
            </Button>
          </div>
        </div>
      </Modal>

      {/* 下载位置选择弹窗 */}
      <DownloadModal
        visible={showDownloadModal}
        onClose={() => setShowDownloadModal(false)}
        onDownload={handleDownloadLocationSelected}
        title="更改自动下载位置"
        selectedFiles={[{ id: 'dummy', name: 'dummy', type: 'folder', path: '/' }]} // 添加一个虚拟的文件，避免"没有选择要下载的文件"的错误
      />
    </>
  );
};

export default SelectFolderModal; 