.time-axis-container {
  position: relative;
  width: 100%;
  height: 120px;
  overflow: hidden;
}

.time-axis-content {
  position: absolute;
  height: 100%;
  transition: transform 0.01s ease-out;
  will-change: transform;
}

.time-marker {
  position: absolute;
  top: 0;
  height: 10%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.time-marker.full.pc {
  height: 4%;
}

.marker-line {
  width: 1px;
  background: #e5e7eb;
  flex-grow: 1;
}

.marker-line.mobile.full.Live {
  display: none;
}

.time-marker.major .marker-line {
  height: 60%;
  background: #94a3b8;
}

.time-label {
  position: absolute;
  top: 15px;
  color: rgba(153, 153, 153, 1);
  white-space: nowrap;
}

.time-label.pc.full.Live {
  top: 8px;
  font-size: 12px;
}

.time-label.mobile.full.Live {
  top: 2px;
  font-size: 5.5px;
}

.time-label.mobile.full.Live.ios {
  top: 2px;
  font-size: 12px;
}

.time-icon {
  position: absolute;
  top: 65%;
}

.event-block {
  position: absolute;
  height: 8px;
  opacity: 0.8;
  transition: opacity 0.2s;
}

.event-block.pc.Live.full,
.event-block.mobile.Live.full {
  height: 3px;
}

.event-block.mobile.Live.full.ios {
  height: 8px;
}

.date-block {
  position: absolute;
  width: 100%;
  height: 100%;
}

/* 滚动条隐藏 */
.time-axis-container::-webkit-scrollbar {
  display: none;
}

.current-time-indicator {
  position: absolute;
  width: 1px;
  height: 72%;
  bottom: 0;
  background: var(--text-color);
  left: 50%;
  z-index: 1;
  pointer-events: none;
}

.current-time-indicator-value {
  position: absolute;
  color: var(--text-color);
  transform: translateY(-70%) translateX(-50%);
}

.current-time-indicator.pc.Live.full,
.current-time-indicator.mobile.Live.full {
  background: #FFF;
  height: 70%;
}

.current-time-indicator-value.pc.Live.full {
  color: #FFF;
  right: -3px;
  top: -4px;
}

.current-time-indicator-value.mobile.Live.full {
  color: #FFF;
}

.event-area-content-LiveText {
  position: absolute;
  top: 15px;
  width: 40px;
  height: 16px;
  background-color: rgba(52, 130, 255, 1);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2px 8px;
  transform: translateX(-50%);
  border-radius: 20px;
  font-family: MiSans W;
  font-weight: 500;
  font-size: 10px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
  vertical-align: middle;
  color: #FFF;
}

.event-area-content-LiveText.mobile {
  top: 15px;
}

.event-area-content-LiveText.mobile.full {
  top: 2px;
  font-size: 8px;
  width: 32px;
  height: 10px;
}

.event-area-content-LiveText.mobile.full.ios {
  top: 2px;
  width: 28px;
  height: 16px;
  transform: translateX(-50%);
  font-size: 12px;
}

.event-area-content-LiveText.pc.full {
  top: 5px;
  font-size: 12px;
  width: 60px;
  height: 20px;
  border-radius: 20px;
  color: rgba(52, 130, 255, 1);
  background-color: rgba(37, 37, 36, 1)
}

.event-area-content {
  background-color: var(--player-control-background-color);
  position: absolute;
  height: 8px;
  top: 38px;
  z-index: 1;
}

.event-area-content.pc.Live.full {
  top: 35%;
}

.event-area-content.mobile.Live.full {
  /* background-color: transparent; */
  top: 18px;
  height: 3px;
}

.event-area-content.mobile.Live.full.ios {
  height: 8px;
  top: 48px;
}

.time-axis-wrapper {
  position: relative;
  width: 100%;
  touch-action: none;
  /* 禁用浏览器默认触摸行为 */
  background-color: var(--card-background-color);
}

.time-axis-wrapper.full.pc.Live,
.time-axis-wrapper.full.mobile.Live {
  background-color: rgba(38, 38, 38, 0.8);
  height: 100%;
}

.axis-arrow {
  position: absolute;
  top: 65%;
  width: 20px;
  height: 20px;
  border: none;
  cursor: pointer;
  z-index: 1;
  transition: all 0.2s;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(-50%);

  img {
    width: 24px;
    height: 24px;
  }
}

.axis-arrow.pc.Live.full {
  top: 50%;
}

.axis-arrow.mobile.Live.full {
  top: 60%;

  img {
    width: 8px;
    height: 8px;
  }
}

.axis-arrow.mobile.Live.full.ios {
  top: 60%;

  img {
    width: 20px;
    height: 20px;
  }
}

.axis-arrow.left {
  left: 25px;
}

.axis-arrow.right {
  right: 25px;
}

.time-axis-oval {
  width: 3px;
  height: 3px;
  border-radius: 3px;
  background-color: rgba(216, 216, 216, 1);
}