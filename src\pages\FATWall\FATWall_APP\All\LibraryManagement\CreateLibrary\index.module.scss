.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--background-color);
  
}

.title {
  font-size: 30px;
  font-weight: 400;
  margin-bottom: 20px;
  color: var(--library-title-color);
  padding: 0px 28px;
}

.formContainer {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;
  padding-bottom: 140px;
}

.formItem {
  border-radius: 16px;
  padding-left: -10px;
  padding: 10px 28px;
}

.input{
  padding: 0px 10px;
  color: var(--list-value-text-color);
}
.inputField {
  width: 100%;
  font-size: 16px;
  padding: 20px;
  height: 64px;
  border-radius: 20px;
  color: var(--library-input-text-color);
  background-color: var(--library-input-bg);
  :global{
    .adm-input-element{
      color: var(--library-input-text-color);
    }
  }
}

.switchRow {
  display: flex;
  justify-content: space-between;
  height: 50px;
  border-radius: 20px;
  padding: 8px 0px;
  align-items: center;
  background-color: var(--background-color);
  :global{
    .adm-switch-checkbox:before{
      background:var(--library-switch-bg);
    }
    .adm-switch-checkbox{
      background:var(--library-switch-bg);
    }
  }
}

.switchLabel {
  color: var(--text-color);
  font-family: MiSans;
  font-size: 17px;
  font-weight: 500;
}

.sectionTitle {
  color: #8C93B0;
  font-family: MiSans;
  font-size: 14px;
  // margin: 16px 0 8px 0;
  padding: 10px 28px;
}

.sourceItem {
  display: flex;
  justify-content: space-between;
  font-family: MiSans;
  font-size: 17px;
  font-weight: 500;
  align-items: center;
  height: 56px;
  border-radius: 16px;
  padding: 5px 28px;
}

.sourceText {
  color: var(--text-color);
  font-size: 16px;
}

.deleteIcon {
  font-size: 20px;
}

.addButton {
  display: flex;
  align-items: center;
  justify-content: space-between;
  // background-color: #F0F0F0;
  font-family: MiSans;
  font-weight: 500;
  border-radius: 16px;
  padding: 12px 28px;
  margin-bottom: 12px;
  color: var(--text-color);
  font-size: 17px;
}

.addIcon {
  margin-right: 4px;
  font-size: 16px;
}

.confirmButton {
  width: 336px;
  height: 50px;
  position: fixed;
  bottom: 35px;
  left: 50%;
  transform: translateX(-50%);
  height: 44px;
  border-radius: 16px;
  background-color: var(--primary-color);
  color: #fff;
  font-size: 17px;
  border: none;
  transition: opacity 0.3s ease;

  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }
}

.errorMessage {
  text-align: center;
  color: red;
  font-size: 14px;
  margin-bottom: 16px;
}

// 添加用户项样式
.userItem {
  margin-bottom: 8px;
  background-color: var(--background-color);

  padding: 6px 28px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .userInfo {
    display: flex;
    align-items: center;
    
    .userAvatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      margin-right: 12px;
      margin-bottom: 10px;
    }

    .userDetails {
      display: flex;
      flex-direction: column;

      .userName {
        font-size: 16px;
        color: var(--library-title-color);
        margin-bottom: 2px;
      }

      .userRole {
        font-size: 12px;
        color: var(--library-title-color);
      }
    }
  }

  .deleteIcon {
    color: #FF5C5C;
    font-size: 18px;
  }

}

.morePopoverContainer{
  :global{
    .adm-popover-inner-content{
      padding: 0px;
      padding-left: 10px;
      // margin-left: 10px;
    }
  }
  .morePopover{
    width: 160px;
    height: 48px;
    display:flex;
    flex-direction: column;
    justify-content: center;
  
  }
}
.divider{
  :global(.adm-divider-horizontal){
    padding: 0 28px !important;
  }
}